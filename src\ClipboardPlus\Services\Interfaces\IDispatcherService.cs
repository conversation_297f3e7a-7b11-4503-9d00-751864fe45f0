using System;
using System.Threading.Tasks;

namespace ClipboardPlus.Services.Interfaces
{
    /// <summary>
    /// Interface pour l'abstraction du dispatcher UI.
    /// Permet l'exécution d'actions sur le thread UI de manière testable.
    /// 
    /// Cette interface respecte le Principe d'Inversion des Dépendances (DIP) :
    /// - Les classes de haut niveau ne dépendent plus de WpfDispatcherService (détail concret)
    /// - Elles dépendent maintenant de IDispatcherService (abstraction)
    /// - Permet l'injection de différentes implémentations (WPF, Test, etc.)
    /// </summary>
    public interface IDispatcherService
    {
        /// <summary>
        /// Vérifie si le thread actuel a accès au dispatcher.
        /// </summary>
        /// <returns>True si le thread actuel peut accéder au dispatcher, false sinon.</returns>
        bool CheckAccess();

        /// <summary>
        /// Exécute une action de manière synchrone sur le thread UI.
        /// </summary>
        /// <param name="action">L'action à exécuter.</param>
        void Invoke(Action action);

        /// <summary>
        /// Exécute une fonction de manière synchrone sur le thread UI et retourne le résultat.
        /// </summary>
        /// <typeparam name="T">Le type de retour de la fonction.</typeparam>
        /// <param name="func">La fonction à exécuter.</param>
        /// <returns>Le résultat de la fonction.</returns>
        T Invoke<T>(Func<T> func);

        /// <summary>
        /// Exécute une action de manière asynchrone sur le thread UI.
        /// </summary>
        /// <param name="action">L'action à exécuter.</param>
        /// <returns>Une tâche représentant l'opération asynchrone.</returns>
        Task InvokeAsync(Action action);

        /// <summary>
        /// Exécute une fonction de manière asynchrone sur le thread UI et retourne le résultat.
        /// </summary>
        /// <typeparam name="T">Le type de retour de la fonction.</typeparam>
        /// <param name="func">La fonction à exécuter.</param>
        /// <returns>Une tâche représentant l'opération asynchrone avec le résultat.</returns>
        Task<T> InvokeAsync<T>(Func<T> func);

        /// <summary>
        /// Exécute une tâche de manière asynchrone sur le thread UI.
        /// </summary>
        /// <param name="taskFunc">La fonction qui retourne une tâche à exécuter.</param>
        /// <returns>Une tâche représentant l'opération asynchrone.</returns>
        Task InvokeAsync(Func<Task> taskFunc);

        /// <summary>
        /// Exécute une tâche de manière asynchrone sur le thread UI et retourne le résultat.
        /// </summary>
        /// <typeparam name="T">Le type de retour de la tâche.</typeparam>
        /// <param name="taskFunc">La fonction qui retourne une tâche à exécuter.</param>
        /// <returns>Une tâche représentant l'opération asynchrone avec le résultat.</returns>
        Task<T> InvokeAsync<T>(Func<Task<T>> taskFunc);
    }
}
