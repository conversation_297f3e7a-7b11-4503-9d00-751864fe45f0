using ClipboardPlus.Core.Services;
using ClipboardPlus.Services;
using ClipboardPlus.Services.Interfaces;
using Moq;
using NUnit.Framework;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace ClipboardPlus.Tests.Unit.Services
{
    [TestFixture]
    public class ClipboardListenerServiceTests
    {
        private Mock<ILoggingService> _mockLogger = null!;
        private Mock<IDispatcherService> _mockDispatcher = null!;
        private ClipboardListenerOptions _options = null!;
        private ClipboardListenerService _service = null!;

        [SetUp]
        public void Setup()
        {
            _mockLogger = new Mock<ILoggingService>();
            _mockDispatcher = new Mock<IDispatcherService>();
            
            // Configurer le mock du dispatcher pour simuler l'exécution sur le thread UI
            _mockDispatcher.Setup(d => d.CheckAccess()).Returns(true);
            _mockDispatcher.Setup(d => d.Invoke(It.IsAny<Action>()))
                .Callback<Action>(action => action());
            _mockDispatcher.Setup(d => d.Invoke<bool>(It.IsAny<Func<bool>>()))
                .Returns<Func<bool>>(func => func());
            
            _options = new ClipboardListenerOptions
            {
                DebounceIntervalMs = 50,
                StartupRetryCount = 1,
                StartupRetryDelayMs = 10,
                UseBlockingUIOperations = true
            };
            
            _service = new ClipboardListenerService(_mockLogger.Object, _mockDispatcher.Object, _options);
        }

        [TearDown]
        public void TearDown()
        {
            _service.Dispose();
        }

        [Test]
        public void IsListening_InitialState_ReturnsFalse()
        {
            // Assert
            Assert.IsFalse(_service.IsListening);
        }

        [Test]
        public void StartListening_WhenCalled_SetsIsListeningToTrue()
        {
            // Arrange
            // Utiliser une implémentation de test qui simule un démarrage réussi
            var testService = new TestClipboardListenerService(_mockLogger.Object, _mockDispatcher.Object, _options);
            
            // Act
            bool result = testService.StartListening();
            
            // Assert
            Assert.IsTrue(result);
            Assert.IsTrue(testService.IsListening);
        }

        [Test]
        public void StopListening_WhenCalled_SetsIsListeningToFalse()
        {
            // Arrange
            var testService = new TestClipboardListenerService(_mockLogger.Object, _mockDispatcher.Object, _options);
            testService.StartListening();
            
            // Act
            testService.StopListening();
            
            // Assert
            Assert.IsFalse(testService.IsListening);
        }

        [Test]
        public void ClipboardContentChanged_WhenTriggered_NotifiesSubscribers()
        {
            // Arrange
            var testService = new TestClipboardListenerService(_mockLogger.Object, _mockDispatcher.Object, _options);
            bool eventRaised = false;
            testService.ClipboardContentChanged += (s, e) => eventRaised = true;
            
            // Act
            testService.SimulateClipboardChange();
            
            // Assert
            Assert.IsTrue(eventRaised);
        }

        [Test]
        public void Dispose_WhenCalled_StopsListening()
        {
            // Arrange
            var testService = new TestClipboardListenerService(_mockLogger.Object, _mockDispatcher.Object, _options);
            testService.StartListening();
            
            // Act
            testService.Dispose();
            
            // Assert
            Assert.IsFalse(testService.IsListening);
        }

        [Test]
        public void StartListening_WhenAlreadyListening_ReturnsFalse()
        {
            // Arrange
            var testService = new TestClipboardListenerService(_mockLogger.Object, _mockDispatcher.Object, _options);
            testService.StartListening();
            
            // Act
            bool result = testService.StartListening();
            
            // Assert
            Assert.IsFalse(result);
        }

        [Test]
        public void StopListening_WhenNotListening_DoesNothing()
        {
            // Arrange
            var testService = new TestClipboardListenerService(_mockLogger.Object, _mockDispatcher.Object, _options);
            
            // Act & Assert (ne devrait pas lever d'exception)
            Assert.DoesNotThrow(() => testService.StopListening());
        }

        [Test]
        public void ClipboardContentChanged_WithDebounce_TriggersOnlyOnce()
        {
            // Arrange
            _options.DebounceIntervalMs = 100;
            var testService = new TestClipboardListenerService(_mockLogger.Object, _mockDispatcher.Object, _options);
            int eventCount = 0;
            testService.ClipboardContentChanged += (s, e) => eventCount++;
            
            // Act
            testService.SimulateClipboardChange();
            testService.SimulateClipboardChange(); // Devrait être ignoré par debounce
            testService.SimulateClipboardChange(); // Devrait être ignoré par debounce
            
            // Assert
            Assert.AreEqual(1, eventCount);
        }

        [Test]
        public async Task ClipboardContentChanged_AfterDebounceInterval_TriggersAgain()
        {
            // Arrange
            _options.DebounceIntervalMs = 50;
            var testService = new TestClipboardListenerService(_mockLogger.Object, _mockDispatcher.Object, _options);
            int eventCount = 0;
            testService.ClipboardContentChanged += (s, e) => eventCount++;
            
            // Act
            testService.SimulateClipboardChange();
            await Task.Delay(100); // Attendre plus que le délai de debounce
            testService.SimulateClipboardChange();
            
            // Assert
            Assert.AreEqual(2, eventCount);
        }
    }

    // Classe de test qui hérite de ClipboardListenerService pour faciliter les tests
    public class TestClipboardListenerService : ClipboardListenerService
    {
        private bool _isListening = false;
        private DateTime _lastClipboardChangeTime = DateTime.MinValue;
        private readonly int _debounceIntervalMs;

        public TestClipboardListenerService(
            ILoggingService loggingService,
            IDispatcherService dispatcherService,
            ClipboardListenerOptions options)
            : base(loggingService, dispatcherService, options)
        {
            _debounceIntervalMs = options.DebounceIntervalMs;
        }

        // Méthode pour simuler un changement de presse-papier
        public void SimulateClipboardChange()
        {
            // Simuler le comportement de debounce
            DateTime now = DateTime.Now;
            if ((now - _lastClipboardChangeTime).TotalMilliseconds < _debounceIntervalMs)
            {
                // Ignorer l'événement si on est dans l'intervalle de debounce
                return;
            }
            
            _lastClipboardChangeTime = now;
            OnClipboardContentChanged(EventArgs.Empty);
        }

        // Surcharge pour simuler un démarrage réussi sans créer de fenêtre réelle
        public override bool StartListening()
        {
            if (_isListening)
                return false;

            _isListening = true;
            return true;
        }

        // Surcharge pour simuler un arrêt sans fenêtre réelle
        public override void StopListening()
        {
            _isListening = false;
        }

        // Surcharge de la propriété pour utiliser notre champ privé
        public new bool IsListening => _isListening;
    }
} 