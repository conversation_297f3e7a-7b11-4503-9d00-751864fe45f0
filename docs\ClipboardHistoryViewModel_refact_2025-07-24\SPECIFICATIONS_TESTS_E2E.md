# Spécifications des Tests End-to-End

**Date :** 2025-07-31
**Objectif :** Dé<PERSON><PERSON>re les scénarios utilisateurs à valider par des tests d'intégration End-to-End.

---

## 🎯 Scénarios Utilisateurs Critiques

### 👤 SCÉNARIO UTILISATEUR #1 : "Je copie du contenu et il apparaît automatiquement dans l'historique"

**🔄 Chemin d'exécution concret** :
1. **Utilisateur** : Sélectionne du contenu (texte, image, fichier) → `Ctrl+C`
2. **Windows** : Déclenche l'événement `ClipboardChanged`.
3. **ClipboardPlus** : Le `ClipboardListenerService` détecte le changement.
4. **Application** : Le `ClipboardItemOrchestrator` traite le nouveau contenu.
5. **Validation** : Le système anti-doublons est vérifié, le format est validé, les métadonnées sont générées.
6. **Persistance** : Le nouvel élément est sauvegardé dans la base de données SQLite.
7. **Interface** : L'historique est mis à jour instantanément pour afficher le nouvel élément.

**✅ Test End-to-End attendu** : `AutomaticCapture_ShouldDetect_AllContentTypes`
- **Validation** : Doit valider la capture de texte, d'images et de chemins de fichiers, avec une persistance complète en base de données.
- **Services critiques à vérifier** : `IClipboardListenerService`, `IClipboardHistoryManager`, `IClipboardItemOrchestrator`.

### 👤 SCÉNARIO UTILISATEUR #2 : "Je clique sur l'icône dans la barre système pour voir mon historique"

**🔄 Chemin d'exécution concret** :
1. **Utilisateur** : Clic gauche sur l'icône de ClipboardPlus dans la barre système.
2. **Windows** : Transmet l'événement de clic à l'application.
3. **ClipboardPlus** : La méthode `SystemTrayService.ShowHistoryWindow()` est appelée.
4. **Interface** : La fenêtre de l'historique s'ouvre.
5. **Chargement** : Le `ClipboardHistoryManager` charge tous les éléments depuis la base de données.
6. **Affichage** : La liste complète des éléments, avec leurs prévisualisations, est affichée à l'utilisateur.

**✅ Test End-to-End attendu** : `SystemTrayClick_ShouldOpen_HistoryWindow_WithAllElements`
- **Validation** : Doit valider l'ouverture de la fenêtre et le chargement complet de l'historique.
- **Services critiques à vérifier** : `ISystemTrayService`, `ISystemTrayOrchestrator`.

### 👤 SCÉNARIO UTILISATEUR #3 : "Je gère mes éléments (copier, épingler, supprimer)"

**🔄 Chemin d'exécution concret** :
1. **Utilisateur** : Sélectionne un élément dans la liste de l'historique.
2. **Action COPIER** : Clic droit → "Copier". Le contenu de l'élément est placé dans le presse-papiers de Windows.
3. **Action ÉPINGLER** : Clic sur l'icône d'épingle. L'élément est marqué comme "épinglé" et reste en haut de la liste.
4. **Action SUPPRIMER** : Clic droit → "Supprimer". L'élément est supprimé de manière permanente de l'historique.
5. **Persistance** : Toutes ces modifications (état d'épinglage, suppression) sont sauvegardées en base de données.

**✅ Test End-to-End attendu** : `ElementActions_ShouldExecute_CopyPinDelete_Operations`
- **Validation** : Doit valider la copie vers le presse-papiers, l'épinglage/désépinglage, et la suppression d'un élément.
- **Services critiques à vérifier** : Les services de suppression, `IClipboardInteractionService`.

### 👤 SCÉNARIO UTILISATEUR #4 : "J'utilise le raccourci clavier pour un accès rapide à l'historique"

**🔄 Chemin d'exécution concret** :
1. **Utilisateur** : Presse la combinaison de touches configurée (ex: `Ctrl+Alt+X`).
2. **Windows** : Le "hook" clavier global détecte la combinaison.
3. **ClipboardPlus** : Le `GlobalShortcutService` reçoit l'événement.
4. **Application** : La méthode `SystemTrayService.ShowHistoryWindow()` est appelée.
5. **Interface** : La fenêtre de l'historique s'ouvre immédiatement.
6. **Accès rapide** : Les éléments récents sont disponibles instantanément pour l'utilisateur.

**✅ Test End-to-End attendu** : `GlobalShortcut_ShouldActivate_QuickHistoryAccess`
- **Validation** : Doit valider la configuration du raccourci, la simulation de son activation et l'accès à l'historique.
- **Services critiques à vérifier** : `IGlobalShortcutService`, `IWindowsHotkeyApi`.
- **Note** : En environnement de test, les hooks système globaux peuvent être simulés plutôt que réellement enregistrés.