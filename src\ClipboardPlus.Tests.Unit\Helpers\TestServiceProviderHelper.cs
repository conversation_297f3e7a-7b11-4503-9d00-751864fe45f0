using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.NewItem.Interfaces;
using ClipboardPlus.Core.Services.NewItem.Implementations;
using ClipboardPlus.Core.Services.NewItem.Models;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using System;
using ClipboardPlus.Core.Services.SupprimerTout;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services.Deletion;
using ClipboardPlus.Modules.History;
using ClipboardPlus.Modules.Commands;
using ClipboardPlus.Modules.Creation;
using Prism.Events;
using ClipboardPlus.Modules.Core.Events;

namespace ClipboardPlus.Tests.Unit.Helpers
{
    /// <summary>
    /// Helper pour créer des ServiceProvider mockés pour les tests.
    /// </summary>
    public static class TestServiceProviderHelper
    {
        /// <summary>
        /// Crée un ServiceProvider mocké avec les services SupprimerTout.
        /// </summary>
        /// <returns>ServiceProvider mocké</returns>
        public static IServiceProvider CreateMockServiceProvider()
        {
            var mockServiceProvider = new Mock<IServiceProvider>();
            var mockLoggingService = new Mock<ILoggingService>();
            var mockClipboardHistoryManager = new Mock<IClipboardHistoryManager>();
            var mockHistoryChangeOrchestrator = new Mock<IHistoryChangeOrchestrator>();
            var mockDeletionService = new Mock<IDeletionService>();
            var mockTestEnvironmentDetector = new Mock<ITestEnvironmentDetector>();
            var mockTestModeHandler = new Mock<ITestModeHandler>();
            var mockValidationService = new Mock<INewItemValidationService>();
            var mockDialogConfigService = new Mock<IDialogConfigurationService>();
            var mockDialogService = new Mock<ClipboardPlus.Core.Services.NewItem.Interfaces.IDialogService>();
            var mockErrorHandlingService = new Mock<IErrorHandlingService>();

            // Mocks pour les modules - Utiliser des mocks bien configurés
            var mockEventAggregator = new Mock<IEventAggregator>();
            var mockClipboardInteractionService = new Mock<IClipboardInteractionService>();
            var mockHistoryModule = new Mock<ClipboardPlus.Modules.History.IHistoryModule>();
            var mockCommandModule = new Mock<ClipboardPlus.Modules.Commands.ICommandModule>();
            var mockCreationModule = new Mock<ClipboardPlus.Modules.Creation.ICreationModule>();

            // Configurer les modules pour qu'ils soient initialisés
            mockHistoryModule.Setup(m => m.State).Returns(ClipboardPlus.Modules.Core.ModuleState.Running);
            mockCommandModule.Setup(m => m.State).Returns(ClipboardPlus.Modules.Core.ModuleState.Running);
            mockCreationModule.Setup(m => m.State).Returns(ClipboardPlus.Modules.Core.ModuleState.Running);

            // Configurer les commandes du CommandModule
            var mockPasteCommand = new Mock<System.Windows.Input.ICommand>();
            mockPasteCommand.Setup(c => c.CanExecute(It.IsAny<object>())).Returns(true);
            mockCommandModule.Setup(m => m.GetCommand("PasteSelectedItem")).Returns(mockPasteCommand.Object);
            mockCommandModule.Setup(m => m.PasteSelectedItemCommand).Returns(mockPasteCommand.Object);

            // Configuration du TestEnvironmentDetector pour retourner true par défaut dans les tests
            mockTestEnvironmentDetector.Setup(x => x.IsInTestEnvironment()).Returns(true);

            // Configuration du TestModeHandler pour utiliser le TestEnvironmentDetector mocké
            mockTestModeHandler.Setup(x => x.IsInTestMode()).Returns(true);

            // Configuration de HandleTestMode pour définir le contenu de test
            mockTestModeHandler.Setup(x => x.HandleTestMode(It.IsAny<IOperationStateManager>()))
                .Callback<IOperationStateManager>(stateManager =>
                {
                    stateManager.NewItemTextContent = "Élément de test";
                    stateManager.IsItemCreationActive = true;
                    stateManager.RefreshItemCreationCommands();
                });

            // Configuration des autres services SOLID requis
            mockValidationService.Setup(x => x.ValidateCanCreateNewItem(It.IsAny<bool>(), It.IsAny<bool>()))
                .Returns(NewItemValidationResult.Success());

            // Les autres services peuvent être des mocks basiques pour les tests

            // Configuration du mock HistoryChangeOrchestrator pour effectuer réellement la synchronisation
            mockHistoryChangeOrchestrator.Setup(x => x.HandleHistoryChangedAsync(It.IsAny<ClipboardPlus.Core.DataModels.HistoryChangedEventArgs>()))
                .Returns<ClipboardPlus.Core.DataModels.HistoryChangedEventArgs>(async (args) =>
                {
                    // Simuler la synchronisation en appelant SynchronizeUIDirectly
                    if (args.SyncContext?.Synchronizer != null)
                    {
                        args.SyncContext.Synchronizer.SynchronizeUIDirectly("mock_orchestrator_sync");
                    }

                    // Si c'est un environnement de test, appeler LoadHistoryAction
                    if (args.Context?.IsInTestEnvironment == true && args.LoadHistoryAction != null)
                    {
                        await args.LoadHistoryAction();
                    }

                    return HistoryChangeResult.Succeeded("test", "Délégation réussie");
                });

            // Instances réelles des services SupprimerTout (pas de mocks pour éviter les problèmes de constructeur)
            var validator = new SupprimerToutValidator(mockLoggingService.Object);
            var analyzer = new SupprimerToutAnalyzer(mockLoggingService.Object);
            var uiHandler = new SupprimerToutUIHandler(mockLoggingService.Object);
            var executor = new SupprimerToutExecutor(mockLoggingService.Object, mockClipboardHistoryManager.Object);
            var orchestrator = new SupprimerToutOrchestrator(
                mockLoggingService.Object,
                validator,
                analyzer,
                uiHandler,
                executor
            );

            // Configuration des retours du ServiceProvider
            mockServiceProvider.Setup(sp => sp.GetService(typeof(ILoggingService)))
                .Returns(mockLoggingService.Object);
            mockServiceProvider.Setup(sp => sp.GetService(typeof(IClipboardHistoryManager)))
                .Returns(mockClipboardHistoryManager.Object);
            mockServiceProvider.Setup(sp => sp.GetService(typeof(SupprimerToutValidator)))
                .Returns(validator);
            mockServiceProvider.Setup(sp => sp.GetService(typeof(SupprimerToutAnalyzer)))
                .Returns(analyzer);
            mockServiceProvider.Setup(sp => sp.GetService(typeof(SupprimerToutUIHandler)))
                .Returns(uiHandler);
            mockServiceProvider.Setup(sp => sp.GetService(typeof(SupprimerToutExecutor)))
                .Returns(executor);
            mockServiceProvider.Setup(sp => sp.GetService(typeof(SupprimerToutOrchestrator)))
                .Returns(orchestrator);
            mockServiceProvider.Setup(sp => sp.GetService(typeof(IHistoryChangeOrchestrator)))
                .Returns(mockHistoryChangeOrchestrator.Object);

            // Configuration du DeletionService pour les tests SupprimerElement
            mockServiceProvider.Setup(sp => sp.GetService(typeof(IDeletionService)))
                .Returns(mockDeletionService.Object);

            // Configuration du TestEnvironmentDetector pour les tests
            mockServiceProvider.Setup(sp => sp.GetService(typeof(ITestEnvironmentDetector)))
                .Returns(mockTestEnvironmentDetector.Object);

            // Configuration du TestModeHandler pour les tests
            mockServiceProvider.Setup(sp => sp.GetService(typeof(ITestModeHandler)))
                .Returns(mockTestModeHandler.Object);

            // Configuration des autres services SOLID requis
            mockServiceProvider.Setup(sp => sp.GetService(typeof(INewItemValidationService)))
                .Returns(mockValidationService.Object);
            mockServiceProvider.Setup(sp => sp.GetService(typeof(IDialogConfigurationService)))
                .Returns(mockDialogConfigService.Object);
            mockServiceProvider.Setup(sp => sp.GetService(typeof(ClipboardPlus.Core.Services.NewItem.Interfaces.IDialogService)))
                .Returns(mockDialogService.Object);
            mockServiceProvider.Setup(sp => sp.GetService(typeof(IErrorHandlingService)))
                .Returns(mockErrorHandlingService.Object);

            // Configuration des modules avec les mocks configurés
            mockServiceProvider.Setup(sp => sp.GetService(typeof(IEventAggregator)))
                .Returns(mockEventAggregator.Object);
            mockServiceProvider.Setup(sp => sp.GetService(typeof(IClipboardInteractionService)))
                .Returns(mockClipboardInteractionService.Object);
            mockServiceProvider.Setup(sp => sp.GetService(typeof(ClipboardPlus.Modules.History.IHistoryModule)))
                .Returns(mockHistoryModule.Object);
            mockServiceProvider.Setup(sp => sp.GetService(typeof(ClipboardPlus.Modules.Commands.ICommandModule)))
                .Returns(mockCommandModule.Object);
            mockServiceProvider.Setup(sp => sp.GetService(typeof(ClipboardPlus.Modules.Creation.ICreationModule)))
                .Returns(mockCreationModule.Object);

            return mockServiceProvider.Object;
        }

        /// <summary>
        /// Crée un ServiceProvider réel avec les services SupprimerTout configurés.
        /// </summary>
        /// <returns>ServiceProvider réel</returns>
        public static IServiceProvider CreateRealServiceProvider()
        {
            // AUCUN PATCH ! Utiliser HostConfiguration.ConfigureServices()
            return HostConfiguration.ConfigureServices();
        }
    }
}
