using System;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Core.Services.Visibility
{
    /// <summary>
    /// Règle de visibilité spécifique pour les titres des éléments
    /// Respecte le Single Responsibility Principle : une seule responsabilité - évaluer la visibilité des titres
    /// Implémente le Strategy Pattern pour permettre l'extensibilité
    /// </summary>
    public class TitleVisibilityRule : IVisibilityRule<ClipboardItem>
    {
        private readonly ILoggingService? _loggingService;

        public TitleVisibilityRule(ILoggingService? loggingService)
        {
            _loggingService = loggingService;
        }

        /// <summary>
        /// Détermine si le titre d'un élément doit être visible
        /// CORRECTION CRITIQUE : Respecte l'intention utilisateur - si "cacher titre" est activé, TOUS les titres sont cachés
        /// </summary>
        /// <param name="item">Élément du presse-papiers à évaluer</param>
        /// <param name="context">Contexte global de visibilité</param>
        /// <returns>True si le titre doit être visible, False sinon</returns>
        public bool ShouldBeVisible(ClipboardItem item, VisibilityContext context)
        {
            try
            {
                // Validation des paramètres
                if (item == null || context == null)
                {
                    _loggingService?.LogInfo($"[TITLE_VISIBILITY_RULE] Paramètres invalides - Item: {item != null}, Context: {context != null}");
                    return false;
                }

                // CORRECTION CRITIQUE : Nouvelle règle métier respectant l'intention utilisateur
                // Si l'utilisateur a activé "cacher titre", TOUS les titres sont cachés, même ceux avec CustomName
                // Les titres ne sont visibles QUE si :
                // 1. La visibilité globale des titres est activée (utilisateur veut voir les titres)
                // 2. ET l'élément a un titre personnalisé non vide (il y a quelque chose à afficher)

                bool hasCustomName = !string.IsNullOrWhiteSpace(item.CustomName);

                // LOGIQUE CORRIGÉE : Respecter le choix utilisateur avant tout
                if (!context.GlobalTitleVisibility)
                {
                    // Si l'utilisateur a choisi de cacher les titres, respecter ce choix ABSOLUMENT
                    _loggingService?.LogInfo($"[TITLE_VISIBILITY_RULE] Item ID: {item.Id}, Titres cachés par choix utilisateur, CustomName: '{item.CustomName}', Result: FALSE");
                    return false;
                }

                // Si l'utilisateur veut voir les titres, les afficher seulement s'il y en a un
                bool result = hasCustomName;

                _loggingService?.LogInfo($"[TITLE_VISIBILITY_RULE] Item ID: {item.Id}, GlobalTitleVisibility: {context.GlobalTitleVisibility}, HasCustomName: {hasCustomName}, CustomName: '{item.CustomName}', Result: {result}");

                return result;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError("[TITLE_VISIBILITY_RULE] Exception in ShouldBeVisible. Returning default value 'true'.", ex);
                return true; // Default to visible in case of error
            }
        }
    }
}
