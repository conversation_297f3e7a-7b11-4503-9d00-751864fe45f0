using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.Deletion;
using ClipboardPlus.Core.Services.Interfaces;
using ClipboardPlus.Core.Services.Implementations;
using WpfApplication = System.Windows.Application;
using WpfClipboard = System.Windows.Clipboard;
using System.Diagnostics;

namespace ClipboardPlus.Core.Services
{
    /// <summary>
    /// Implémentation du gestionnaire d'historique du presse-papiers.
    /// </summary>
    public class ClipboardHistoryManager : IClipboardHistoryManager
    {
        private readonly IPersistenceService _persistenceService;
        private readonly ISettingsManager _settingsManager;
        private readonly IClipboardInteractionService _clipboardInteractionService;
        private List<ClipboardItem> _historyItems = new List<ClipboardItem>();
        private bool _isUpdatingItem = false; // Drapeau pour éviter les rechargements complets lors des mises à jour
        private ILoggingService? _loggingService;

        // Services de suppression refactorisés
        private readonly IDeletionValidator? _deletionValidator;
        private readonly IDeletionMemoryService? _deletionMemoryService;
        private readonly IDeletionRetryService? _deletionRetryService;
        private readonly IDeletionStateManager? _deletionStateManager;
        private readonly IDeletionNotificationService? _deletionNotificationService;

        // Orchestrateur pour la nouvelle implémentation SOLID (lazy initialization)
        private IClipboardItemOrchestrator? _orchestrator;

        /// <summary>
        /// Liste des éléments dans l'historique du presse-papiers.
        /// </summary>
        public List<ClipboardItem> HistoryItems => _historyItems;

        /// <summary>
        /// Événement déclenché lorsque l'historique change (ajout, suppression, modification).
        /// </summary>
        public event EventHandler? HistoryChanged;

        /// <summary>
        /// Constructeur.
        /// </summary>
        /// <param name="persistenceService">Service de persistance.</param>
        /// <param name="settingsManager">Gestionnaire de paramètres.</param>
        /// <param name="clipboardInteractionService">Service d'interaction avec le presse-papiers.</param>
        public ClipboardHistoryManager(IPersistenceService persistenceService, ISettingsManager settingsManager, IClipboardInteractionService clipboardInteractionService)
        {
            _persistenceService = persistenceService ?? throw new ArgumentNullException(nameof(persistenceService));
            _settingsManager = settingsManager ?? throw new ArgumentNullException(nameof(settingsManager));
            _clipboardInteractionService = clipboardInteractionService ?? throw new ArgumentNullException(nameof(clipboardInteractionService));
            _loggingService = GetLoggingService();

            _loggingService?.LogInfo("ClipboardHistoryManager initialisé");

            // Chargement initial des éléments
            _ = LoadHistoryAsync();
        }

        /// <summary>
        /// Constructeur avec services de suppression refactorisés.
        /// </summary>
        /// <param name="persistenceService">Service de persistance.</param>
        /// <param name="settingsManager">Gestionnaire de paramètres.</param>
        /// <param name="clipboardInteractionService">Service d'interaction avec le presse-papiers.</param>
        /// <param name="deletionValidator">Service de validation des suppressions.</param>
        /// <param name="deletionMemoryService">Service de suppression en mémoire.</param>
        /// <param name="deletionRetryService">Service de retry pour les suppressions.</param>
        /// <param name="deletionStateManager">Service de gestion d'état des suppressions.</param>
        /// <param name="deletionNotificationService">Service de notification des suppressions.</param>
        public ClipboardHistoryManager(
            IPersistenceService persistenceService,
            ISettingsManager settingsManager,
            IClipboardInteractionService clipboardInteractionService,
            IDeletionValidator deletionValidator,
            IDeletionMemoryService deletionMemoryService,
            IDeletionRetryService deletionRetryService,
            IDeletionStateManager deletionStateManager,
            IDeletionNotificationService deletionNotificationService)
        {
            _persistenceService = persistenceService ?? throw new ArgumentNullException(nameof(persistenceService));
            _settingsManager = settingsManager ?? throw new ArgumentNullException(nameof(settingsManager));
            _clipboardInteractionService = clipboardInteractionService ?? throw new ArgumentNullException(nameof(clipboardInteractionService));
            _deletionValidator = deletionValidator ?? throw new ArgumentNullException(nameof(deletionValidator));
            _deletionMemoryService = deletionMemoryService ?? throw new ArgumentNullException(nameof(deletionMemoryService));
            _deletionRetryService = deletionRetryService ?? throw new ArgumentNullException(nameof(deletionRetryService));
            _deletionStateManager = deletionStateManager ?? throw new ArgumentNullException(nameof(deletionStateManager));

            _loggingService = GetLoggingService();

            _deletionNotificationService = deletionNotificationService ?? throw new ArgumentNullException(nameof(deletionNotificationService));

            _loggingService?.LogInfo("ClipboardHistoryManager initialisé avec services de suppression refactorisés");

            // Chargement initial des éléments
            _ = LoadHistoryAsync();
        }

        /// <summary>
        /// Trouve un élément existant qui est un doublon de l'élément fourni.
        /// Un doublon est défini par un DataType identique et des RawData identiques.
        /// </summary>
        /// <param name="itemToTest">L'élément à vérifier.</param>
        /// <returns>L'élément ClipboardItem existant s'il est un doublon, sinon null.</returns>
        public async Task<ClipboardItem?> FindDuplicateAsync(ClipboardItem? itemToTest)
        {
            var operationId = Guid.NewGuid().ToString().Substring(0, 8);
            _loggingService?.LogInfo($"[{operationId}][DÉBUT] FindDuplicateAsync - Test pour Type: {itemToTest?.DataType}, Taille RawData: {itemToTest?.RawData?.Length ?? 0}");

            if (itemToTest?.RawData == null || itemToTest.RawData.Length == 0)
            {
                _loggingService?.LogInfo($"[{operationId}] FindDuplicateAsync: itemToTest ou RawData est null/vide. Non considéré comme doublon.");
                return null; // Ne peut pas être un doublon si les données brutes sont vides
            }

            List<ClipboardItem> currentItems = await _persistenceService.GetAllClipboardItemsAsync();
            _loggingService?.LogInfo($"[{operationId}] FindDuplicateAsync: {currentItems.Count} éléments récupérés de la BDD pour comparaison.");

            foreach (var existingItem in currentItems)
            {
                if (existingItem.DataType == itemToTest.DataType && 
                    existingItem.RawData != null && 
                    itemToTest.RawData.SequenceEqual(existingItem.RawData))
                {
                    _loggingService?.LogInfo($"[{operationId}][FIN] FindDuplicateAsync - Doublon trouvé. ID existant: {existingItem.Id}");
                    return existingItem; // Retourne l'objet existant
                }
            }

            _loggingService?.LogInfo($"[{operationId}][FIN] FindDuplicateAsync - Aucun doublon trouvé.");
            return null;
        }

        /// <summary>
        /// Ajoute un élément à l'historique du presse-papiers.
        /// </summary>
        /// <param name="item">L'élément à ajouter.</param>
        /// <returns>L'ID de l'élément ajouté.</returns>
        public async Task<long> AddItemAsync(ClipboardItem? item)
        {
            if (item == null)
            {
                throw new ArgumentNullException(nameof(item));
            }

            var orchestrator = GetOrCreateOrchestrator();
            return await orchestrator.AddItemAsync(item);
        }

        /// <summary>
        /// Crée ou retourne l'orchestrateur SOLID pour AddItemAsync.
        /// Utilise une approche lazy pour éviter les dépendances circulaires.
        /// </summary>
        /// <returns>L'orchestrateur configuré</returns>
        private IClipboardItemOrchestrator GetOrCreateOrchestrator()
        {
            if (_orchestrator != null)
            {
                return _orchestrator;
            }

            // Créer les services SOLID pour l'orchestrateur
            var validator = new ClipboardItemValidator(_loggingService);
            var duplicateDetector = new DuplicateDetector(_loggingService);
            var processor = new ClipboardItemProcessor(_persistenceService, _loggingService);
            var historyManager = new HistoryManagerWrapper(_historyItems, _persistenceService, _loggingService);

            // Créer un callback qui appelle notre événement HistoryChanged
            Action? historyChangedCallback = () => HistoryChanged?.Invoke(this, EventArgs.Empty);
            var eventNotifier = new EventNotifier(historyChangedCallback, _loggingService);
            var operationLogger = new OperationLogger(_loggingService);

            return new ClipboardItemOrchestrator(
                validator,
                duplicateDetector,
                processor,
                historyManager,
                eventNotifier,
                operationLogger,
                _settingsManager);
        }

        /// <summary>
        /// Force la notification de changement d'historique même si _isUpdatingItem est true.
        /// Utilisé dans des cas spécifiques où la notification est essentielle.
        /// </summary>
        private void ForceHistoryChangedNotification()
        {
            var loggingService = GetLoggingService();
            loggingService?.LogInfo("ForceHistoryChangedNotification: Notification forcée des abonnés");
            HistoryChanged?.Invoke(this, EventArgs.Empty);
        }

        /// <summary>
        /// Met à jour un élément existant dans l'historique.
        /// </summary>
        /// <param name="item">L'élément à mettre à jour.</param>
        public async Task UpdateItemAsync(ClipboardItem? item)
        {
            var operationId = Guid.NewGuid().ToString().Substring(0, 8);
            _loggingService?.LogInfo($"[{operationId}][DÉBUT] UpdateItemAsync - ID: {item?.Id}, Type: {item?.DataType}, Nom: '{item?.CustomName ?? "N/A"}', IsPinned: {item?.IsPinned}, ThreadID: {System.Environment.CurrentManagedThreadId}, Heure: {System.DateTime.Now:HH:mm:ss.fff}");

            if (item == null)
            {
                _loggingService?.LogError($"[{operationId}] UpdateItemAsync: L'élément fourni est null.");
                throw new ArgumentNullException(nameof(item));
            }

            bool success = false;
            try
            {
                // Activer le drapeau pour éviter le rechargement complet de l'historique pendant cette mise à jour spécifique.
                _isUpdatingItem = true;
                _loggingService?.LogInfo($"[{operationId}] UpdateItemAsync: _isUpdatingItem défini à true.");

                await _persistenceService.UpdateClipboardItemAsync(item);
                _loggingService?.LogInfo($"[{operationId}] UpdateItemAsync: Élément ID={item.Id} mis à jour dans la base de données.");

            // Mettre à jour l'élément dans la liste en mémoire
            int index = _historyItems.FindIndex(i => i.Id == item.Id);
            if (index >= 0)
            {
                _historyItems[index] = item;
                    _loggingService?.LogInfo($"[{operationId}] UpdateItemAsync: Élément ID={item.Id} mis à jour en mémoire à l'index {index}.");
                    success = true;
                }
                else
                {
                    _loggingService?.LogWarning($"[{operationId}] UpdateItemAsync: Élément ID={item.Id} non trouvé en mémoire. Il ne sera pas mis à jour localement mais la BDD a pu être mise à jour.");
                }

                // DIAGNOSTIC CRITIQUE : Logger l'état de l'élément avant notification
            _loggingService?.LogInfo($"[{operationId}] AVANT OnHistoryChanged - Item ID: {item.Id}, CustomName: '{item.CustomName}', IsTitleVisible: {item.IsTitleVisible}");

            // Notifier du changement. Si _isUpdatingItem est true, OnHistoryChanged ne devrait rien faire.
                // Cependant, certains scénarios (ex: épinglage) pourraient nécessiter une notification.
                // Le drapeau _isUpdatingItem est surtout pour éviter le RECHARGEMENT COMPLET de la liste.
                // Une notification simple de changement de propriété sur l'item lui-même est souvent gérée par le binding WPF.
            OnHistoryChanged();
                _loggingService?.LogInfo($"[{operationId}] UpdateItemAsync: Notification OnHistoryChanged potentiellement envoyée (sera ignorée si _isUpdatingItem est respecté).");
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"[{operationId}] UpdateItemAsync: Erreur lors de la mise à jour de l'élément ID={item.Id}. Message: {ex.Message}", ex);
            }
            finally
            {
                _isUpdatingItem = false;
                _loggingService?.LogInfo($"[{operationId}] UpdateItemAsync: _isUpdatingItem réinitialisé à false.");

                // ✅ CORRECTION CRITIQUE: Déclencher OnHistoryChanged après avoir remis _isUpdatingItem à false
                // Cela permet de notifier l'UI des changements (comme l'épinglage) qui ont été ignorés pendant la mise à jour
                if (success)
                {
                    _loggingService?.LogInfo($"[{operationId}] UpdateItemAsync: Déclenchement OnHistoryChanged après mise à jour réussie");
                    OnHistoryChanged();
                }

                _loggingService?.LogInfo($"[{operationId}][FIN] UpdateItemAsync - ID: {item.Id}, Succès de mise à jour en mémoire: {success}.");
            }
        }

        /// <summary>
        /// Utilise un élément de l'historique en le copiant dans le presse-papiers.
        /// </summary>
        /// <param name="id">L'ID de l'élément à utiliser.</param>
        public async Task UseItemAsync(long id)
        {
            var operationId = Guid.NewGuid().ToString().Substring(0, 8);
            _loggingService?.LogInfo($"[{operationId}][DÉBUT] UseItemAsync - ID: {id}");

            var itemToUse = _historyItems.FirstOrDefault(i => i.Id == id);

            if (itemToUse == null)
            {
                _loggingService?.LogWarning($"[{operationId}] UseItemAsync: Item with ID {id} not found.");
                throw new KeyNotFoundException($"Item with ID {id} not found in history.");
            }

            // Pour l'instant, nous ne gérons que le texte.
            if (itemToUse.DataType != ClipboardDataType.Text || itemToUse.RawData == null)
            {
                _loggingService?.LogWarning($"[{operationId}] UseItemAsync: Item with ID {id} is not of a supported type for pasting (Text only for now).");
                throw new NotSupportedException("Pasting non-text content is not yet supported.");
            }

            try
            {
                var textContent = System.Text.Encoding.UTF8.GetString(itemToUse.RawData);
                bool success = await _clipboardInteractionService.SetClipboardContentAsync(textContent);

                if (success)
                {
                    _loggingService?.LogInfo($"[{operationId}] UseItemAsync: Successfully set clipboard content for item ID {id}.");
                    // Ne plus mettre à jour le timestamp ni l'élément pour éviter le réordonnancement
                }
                else
                {
                    _loggingService?.LogError($"[{operationId}] UseItemAsync: Failed to set clipboard content for item ID {id}.");
                    throw new InvalidOperationException("Failed to set clipboard content.");
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"[{operationId}] UseItemAsync: An error occurred while using item ID {id}.", ex);
                throw;
            }
        }

        /// <summary>
        /// Efface tous les éléments de l'historique, avec option de préserver les éléments épinglés.
        /// </summary>
        /// <param name="preservePinned">Si vrai, les éléments épinglés ne seront pas supprimés.</param>
        public async Task ClearHistoryAsync(bool preservePinned = true)
        {
            string operationId = Guid.NewGuid().ToString().Substring(0, 8);
            _loggingService?.LogInfo($"[{operationId}][DÉBUT] ClearHistoryAsync - PreservePinned: {preservePinned}, ThreadID: {System.Environment.CurrentManagedThreadId}, Heure: {System.DateTime.Now:HH:mm:ss.fff}");

            try
            {
                _loggingService?.LogInfo($"[{operationId}] ClearHistoryAsync: Appel de _persistenceService.ClearClipboardItemsAsync(preservePinned={preservePinned}).");
                await _persistenceService.ClearClipboardItemsAsync(preservePinned);
                _loggingService?.LogInfo($"[{operationId}] ClearHistoryAsync: Opération ClearClipboardItemsAsync terminée.");

            // Mettre à jour la liste en mémoire
            if (preservePinned)
            {
                    int itemsBeforeRemoval = _historyItems.Count;
                _historyItems.RemoveAll(i => !i.IsPinned);
                    _loggingService?.LogInfo($"[{operationId}] ClearHistoryAsync: Éléments non épinglés supprimés de la mémoire. Avant: {itemsBeforeRemoval}, Après: {_historyItems.Count}.");
            }
            else
            {
                    int itemsBeforeClear = _historyItems.Count;
                _historyItems.Clear();
                    _loggingService?.LogInfo($"[{operationId}] ClearHistoryAsync: Tous les éléments supprimés de la mémoire. Avant: {itemsBeforeClear}, Après: {_historyItems.Count}.");
            }

            // Notifier du changement
                // Il est important de notifier après la modification de la collection en mémoire.
                // OnHistoryChanged gère déjà le drapeau _isUpdatingItem si nécessaire (mais pour Clear, on veut notifier).
                _loggingService?.LogInfo($"[{operationId}] ClearHistoryAsync: Notification du changement d'historique.");
            OnHistoryChanged();
                _loggingService?.LogInfo($"[{operationId}][FIN] ClearHistoryAsync - PreservePinned: {preservePinned}. Opération réussie.");
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"[{operationId}][ERREUR] ClearHistoryAsync - PreservePinned: {preservePinned}. Exception: {ex.Message}", ex);
                // En cas d'erreur, il est possible que la liste en mémoire et la base de données soient désynchronisées.
                // Une synchronisation complète pourrait être nécessaire lors du prochain chargement ou manuellement.
                // Pour l'instant, nous propageons l'exception pour une gestion de plus haut niveau si nécessaire.
                throw;
            }
            finally
            {
                _loggingService?.ForceFlush(); // S'assurer que les logs sont écrits.
            }
        }

        /// <summary>
        /// Charge les éléments de l'historique depuis la base de données.
        /// </summary>
        private async System.Threading.Tasks.Task LoadHistoryAsync()
        {
            var loggingService = GetLoggingService();
            loggingService?.LogInfo($"[DÉBUT] LoadHistoryAsync - ThreadID: {Environment.CurrentManagedThreadId}, Heure: {DateTime.Now:HH:mm:ss.fff}");

            try
            {
                var loadedItems = await _persistenceService.GetAllClipboardItemsAsync();
                _historyItems = loadedItems ?? new List<ClipboardItem>();
                loggingService?.LogInfo($"[FIN] LoadHistoryAsync - {_historyItems.Count} élément(s) chargé(s)");
            }
            catch (Exception ex)
            {
                // Logger l'erreur
                loggingService?.LogError($"LoadHistoryAsync: Erreur lors du chargement: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Charge les éléments de l'historique depuis la base de données sans déclencher d'événement HistoryChanged.
        /// Cette méthode est utilisée pour éviter les boucles infinies lors de la synchronisation.
        /// </summary>
        public async System.Threading.Tasks.Task LoadHistorySilentlyAsync()
        {
            await LoadHistoryAsync();
        }

        /// <summary>
        /// Limite le nombre d'éléments dans l'historique selon les paramètres.
        /// </summary>
        private async Task EnforceMaxHistoryItemsAsync()
        {
            var loggingService = GetLoggingService();
            var operationId = Guid.NewGuid().ToString().Substring(0, 8);
            try
            {
            int maxItems = _settingsManager.MaxHistoryItems;
            if (_historyItems.Count > maxItems)
            {
                    loggingService?.LogInfo($"[{operationId}] EnforceMaxHistoryItemsAsync: Le nombre d'éléments ({_historyItems.Count}) dépasse le maximum ({maxItems}).");
                    
                    var itemsToRemove = _historyItems
                    .Where(i => !i.IsPinned)
                    .OrderBy(i => i.Timestamp)
                        .Take(_historyItems.Count - maxItems)
                    .ToList();

                    if (itemsToRemove.Any())
                    {
                        loggingService?.LogInfo($"[{operationId}] EnforceMaxHistoryItemsAsync: Suppression de {itemsToRemove.Count} élément(s) non épinglé(s) le(s) plus ancien(s).");
                        foreach (var item in itemsToRemove)
                        {
                            await _persistenceService.DeleteClipboardItemAsync(item.Id);
                            _historyItems.Remove(item);
                            loggingService?.LogDeletion($"[AutoPurge] ItemID: {item.Id}, Type: {item.DataType}, Preview: '{item.TextPreview}'");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                loggingService?.LogError($"[{operationId}] EnforceMaxHistoryItemsAsync: Erreur lors de l'application de la limite d'historique. Message: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Déclenche l'événement HistoryChanged.
        /// </summary>
        protected virtual void OnHistoryChanged()
        {
            var loggingService = GetLoggingService();
            loggingService?.LogInfo($"OnHistoryChanged: _isUpdatingItem={_isUpdatingItem}");

            // Ne pas recharger la liste complète si nous sommes en train de mettre à jour un seul élément
            if (_isUpdatingItem)
            {
                loggingService?.LogInfo("OnHistoryChanged: Notification ignorée car _isUpdatingItem est true");
                return;
            }

            loggingService?.LogInfo("OnHistoryChanged: Notification des abonnés");
            HistoryChanged?.Invoke(this, EventArgs.Empty);
        }

        /// <summary>
        /// Méthode publique pour déclencher l'événement HistoryChanged depuis l'extérieur.
        /// Utilisée par le DeletionNotificationService pour notifier les changements.
        /// </summary>
        public void NotifyHistoryChanged()
        {
            var loggingService = GetLoggingService();
            loggingService?.LogInfo("NotifyHistoryChanged: Déclenchement externe de l'événement HistoryChanged");
            OnHistoryChanged();
        }

        /// <summary>
        /// Persiste le nouvel ordre des éléments après une opération de glisser-déposer.
        /// </summary>
        /// <param name="itemsInNewOrder">Les éléments dans leur nouvel ordre.</param>
        /// <returns>Tâche asynchrone.</returns>
        public async Task PersistNewItemOrderAsync(IEnumerable<ClipboardItem> itemsInNewOrder)
        {
            if (itemsInNewOrder == null)
                throw new ArgumentNullException(nameof(itemsInNewOrder));

            var loggingService = GetLoggingService();
            loggingService?.LogInfo($"[DÉBUT] PersistNewItemOrderAsync - Thread ID: {Environment.CurrentManagedThreadId}");

            try
            {
                // Convertir en liste pour faciliter l'accès par index
                var items = itemsInNewOrder.ToList();
                loggingService?.LogInfo($"PersistNewItemOrderAsync: {items.Count} élément(s) à traiter");

                // Mettre à jour l'ordre des éléments
                for (int i = 0; i < items.Count; i++)
                {
                    var item = items[i];
                    
                    // Mettre à jour l'index d'ordre
                    item.OrderIndex = items.Count - i; // Ordre inversé pour que les éléments les plus récents soient en haut
                    loggingService?.LogInfo($"PersistNewItemOrderAsync: Élément ID={item.Id}, nouvel OrderIndex={item.OrderIndex}");
                    
                    // Persister les changements
                    await _persistenceService.UpdateClipboardItemAsync(item);
                }

                // IMPORTANT: Mettre à jour la liste en mémoire en conservant UNIQUEMENT les éléments présents dans itemsInNewOrder
                // Cela empêche les éléments supprimés de réapparaître
                _historyItems = items.ToList();
                loggingService?.LogInfo($"PersistNewItemOrderAsync: Liste en mémoire mise à jour avec {_historyItems.Count} élément(s)");

                // Notifier du changement
                loggingService?.LogInfo("PersistNewItemOrderAsync: Notification des changements");
                OnHistoryChanged();
                
                loggingService?.LogInfo($"[FIN] PersistNewItemOrderAsync - Succès");
            }
            catch (Exception ex)
            {
                // Logger l'erreur
                loggingService?.LogError($"PersistNewItemOrderAsync: Exception: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// Obtient une instance du service de journalisation.
        /// </summary>
        private ILoggingService? GetLoggingService()
        {
            try
            {
                if (WpfApplication.Current != null)
                {
                    var services = WpfApplication.Current.GetType().GetProperty("Services")?.GetValue(WpfApplication.Current);
                    if (services != null)
                    {
                        var getService = services.GetType().GetMethod("GetService", new Type[] { typeof(Type) });
                        if (getService != null)
                        {
                            return getService.Invoke(services, new object[] { typeof(ILoggingService) }) as ILoggingService;
                        }
                    }
                }
            }
            catch (Exception)
            {
                // Ignorer les erreurs, retourner null en cas d'échec
            }
            return null;
        }

        /// <summary>
        /// Supprime un élément de l'historique de manière asynchrone.
        /// Cette méthode utilise une architecture SOLID avec des services modulaires pour la validation,
        /// la suppression en mémoire, les tentatives de retry, la gestion d'état et les notifications.
        /// </summary>
        /// <param name="id">L'ID de l'élément à supprimer.</param>
        /// <returns>True si la suppression a réussi, False sinon.</returns>
        public async Task<bool> DeleteItemAsync(long id)
        {
            // Vérifier que les services de suppression sont disponibles
            if (_deletionValidator == null || _deletionMemoryService == null ||
                _deletionRetryService == null || _deletionStateManager == null ||
                _deletionNotificationService == null)
            {
                _loggingService?.LogError("DeleteItemAsync: Services de suppression non disponibles. L'injection de dépendances n'a pas été configurée correctement.");
                throw new InvalidOperationException(
                    "Les services de suppression ne sont pas disponibles. " +
                    "Vérifiez que l'injection de dépendances a été configurée correctement dans HostConfiguration.cs. " +
                    "Services requis: DeletionValidator, DeletionMemoryService, DeletionRetryService, DeletionStateManager, DeletionNotificationService."
                );
            }

            string operationId = Guid.NewGuid().ToString().Substring(0, 8);
            _loggingService?.LogInfo($"[{operationId}][DÉBUT] DeleteItemAsync - ID={id}, ThreadID: {System.Environment.CurrentManagedThreadId}, Heure: {System.DateTime.Now:HH:mm:ss.fff}");

            try
            {
                // Étape 1: Validation des paramètres
                var validationResult = await _deletionValidator.ValidateRequestAsync(id, operationId);
                if (!validationResult.IsValid)
                {
                    _loggingService?.LogWarning($"[{operationId}] DeleteItemAsync: Validation échouée - {validationResult.ErrorMessage}");
                    return false;
                }

                // Étape 2: Suppression en mémoire
                var memoryResult = await _deletionMemoryService.RemoveFromMemoryAsync(id, _historyItems, operationId);

                // Étape 3: Suppression en base de données avec retry
                var databaseResult = await _deletionRetryService.DeleteFromDatabaseWithRetryAsync(id, operationId);

                // Étape 4: Détermination du résultat global
                var globalResult = await _deletionStateManager.DetermineGlobalResultAsync(memoryResult, databaseResult, operationId);

                // Étape 5: Notification conditionnelle
                var notificationResult = await _deletionNotificationService.NotifyAsync(globalResult, operationId);

                _loggingService?.LogInfo($"[{operationId}][FIN] DeleteItemAsync - ID={id}, Succès: {globalResult.Success}, Notification: {notificationResult.Success}");

                return globalResult.Success;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"[{operationId}][EXCEPTION] DeleteItemAsync - ID={id}: {ex.Message}", ex);
                throw;
            }
            finally
            {
                _loggingService?.LogInfo($"[{operationId}] DeleteItemAsync: Opération finale pour ID={id}.");
                _loggingService?.ForceFlush();
            }
        }

        /// <summary>
        /// Nettoie la base de données en supprimant tous les éléments qui ne sont pas présents
        /// dans la liste en mémoire.
        /// </summary>
        /// <returns>Nombre d'éléments supprimés de la base de données.</returns>
        public async Task<int> PurgeOrphanedItemsAsync()
        {
            var loggingService = GetLoggingService();
            loggingService?.LogInfo($"[DÉBUT] PurgeOrphanedItemsAsync - ThreadID: {Environment.CurrentManagedThreadId}, Heure: {DateTime.Now:HH:mm:ss.fff}");
            
            if (_historyItems == null)
            {
                loggingService?.LogWarning("PurgeOrphanedItemsAsync: La collection d'éléments d'historique n'est pas initialisée");
                return 0;
            }
            
            try
            {
                // Récupérer tous les éléments de la base de données
                var allDbItems = await _persistenceService.GetAllClipboardItemsAsync();
                
                // Si aucun élément en base, rien à faire
                if (allDbItems == null || allDbItems.Count == 0)
                {
                    loggingService?.LogInfo("PurgeOrphanedItemsAsync: Aucun élément en base de données, rien à purger");
                    return 0;
                }
                
                // Créer un ensemble des IDs en mémoire pour une recherche efficace
                var memoryIds = new HashSet<long>(_historyItems.Select(item => item.Id));
                
                // Trouver les éléments orphelins (présents en base mais pas en mémoire)
                var orphanedItems = allDbItems.Where(item => !memoryIds.Contains(item.Id)).ToList();
                
                loggingService?.LogInfo($"PurgeOrphanedItemsAsync: {orphanedItems.Count} élément(s) orphelin(s) trouvé(s) sur {allDbItems.Count} élément(s) total en base");
                
                // Si aucun orphelin, rien à faire
                if (orphanedItems.Count == 0)
                {
                    loggingService?.LogInfo("PurgeOrphanedItemsAsync: Aucun élément orphelin à supprimer");
                    return 0;
                }
                
                // Supprimer chaque élément orphelin de la base de données
                int successfulDeletions = 0;
                foreach (var item in orphanedItems)
                {
                    try
                    {
                        loggingService?.LogInfo($"PurgeOrphanedItemsAsync: Suppression de l'élément orphelin ID={item.Id}, Type={item.DataType}, CustomName='{item.CustomName ?? "N/A"}'");
                        
                        bool deleted = await _persistenceService.DeleteClipboardItemAsync(item.Id);
                        if (deleted)
                        {
                            successfulDeletions++;
                            loggingService?.LogInfo($"PurgeOrphanedItemsAsync: Élément ID={item.Id} supprimé avec succès");
                        }
                        else
                        {
                            loggingService?.LogWarning($"PurgeOrphanedItemsAsync: Échec de la suppression de l'élément ID={item.Id}");
                        }
                    }
                    catch (Exception ex)
                    {
                        loggingService?.LogError($"PurgeOrphanedItemsAsync: Erreur lors de la suppression de l'élément ID={item.Id}: {ex.Message}", ex);
                    }
                }
                
                loggingService?.LogInfo($"[FIN] PurgeOrphanedItemsAsync - {successfulDeletions}/{orphanedItems.Count} élément(s) orphelin(s) supprimé(s)");
                return successfulDeletions;
            }
            catch (Exception ex)
            {
                loggingService?.LogError($"PurgeOrphanedItemsAsync: Erreur générale: {ex.Message}", ex);
                return 0;
            }
        }

        /// <summary>
        /// Supprime les éléments non épinglés plus anciens que la période spécifiée.
        /// </summary>
        /// <param name="olderThan">Période avant laquelle supprimer les éléments</param>
        /// <returns>Nombre d'éléments supprimés</returns>
        public async Task<int> ClearItemsOlderThanAsync(TimeSpan olderThan)
        {
            var loggingService = GetLoggingService();
            var operationId = Guid.NewGuid().ToString().Substring(0, 8);
            loggingService?.LogInfo($"[{operationId}][DÉBUT] ClearItemsOlderThanAsync - OlderThan: {olderThan}, ThreadID: {Environment.CurrentManagedThreadId}, Heure: {DateTime.Now:HH:mm:ss.fff}");

            try
            {
                var cutoffDate = DateTime.Now - olderThan;
                loggingService?.LogInfo($"[{operationId}] ClearItemsOlderThanAsync: Date de coupure calculée: {cutoffDate:yyyy-MM-dd HH:mm:ss}");

                var itemsToRemove = _historyItems
                    .Where(i => !i.IsPinned && i.Timestamp < cutoffDate)
                    .ToList();

                loggingService?.LogInfo($"[{operationId}] ClearItemsOlderThanAsync: {itemsToRemove.Count} élément(s) non épinglé(s) trouvé(s) plus ancien(s) que {cutoffDate:yyyy-MM-dd HH:mm:ss}");

                if (itemsToRemove.Count == 0)
                {
                    loggingService?.LogInfo($"[{operationId}] ClearItemsOlderThanAsync: Aucun élément à supprimer");
                    return 0;
                }

                int successfulDeletions = 0;
                foreach (var item in itemsToRemove)
                {
                    try
                    {
                        loggingService?.LogInfo($"[{operationId}] ClearItemsOlderThanAsync: Suppression de l'élément ID={item.Id}, Timestamp={item.Timestamp:yyyy-MM-dd HH:mm:ss}, Type={item.DataType}");

                        bool deleted = await _persistenceService.DeleteClipboardItemAsync(item.Id);
                        if (deleted)
                        {
                            _historyItems.Remove(item);
                            successfulDeletions++;
                            loggingService?.LogDeletion($"[DateCleanup] ItemID: {item.Id}, Type: {item.DataType}, Timestamp: {item.Timestamp:yyyy-MM-dd HH:mm:ss}, Preview: '{item.TextPreview}'");
                        }
                        else
                        {
                            loggingService?.LogWarning($"[{operationId}] ClearItemsOlderThanAsync: Échec de la suppression de l'élément ID={item.Id}");
                        }
                    }
                    catch (Exception ex)
                    {
                        loggingService?.LogError($"[{operationId}] ClearItemsOlderThanAsync: Erreur lors de la suppression de l'élément ID={item.Id}: {ex.Message}", ex);
                    }
                }

                // Notifier du changement si des éléments ont été supprimés
                if (successfulDeletions > 0)
                {
                    loggingService?.LogInfo($"[{operationId}] ClearItemsOlderThanAsync: Notification du changement d'historique");
                    OnHistoryChanged();
                }

                loggingService?.LogInfo($"[{operationId}][FIN] ClearItemsOlderThanAsync - {successfulDeletions}/{itemsToRemove.Count} élément(s) supprimé(s) avec succès");
                return successfulDeletions;
            }
            catch (Exception ex)
            {
                loggingService?.LogError($"[{operationId}] ClearItemsOlderThanAsync: Erreur générale: {ex.Message}", ex);
                throw;
            }
            finally
            {
                loggingService?.ForceFlush();
            }
        }
    }
}