### **Prompt pour la Mission d'Unification Architecturale**

"J'ai besoin de ton aide pour une mission de nettoyage architectural critique. Une analyse a révélé une incohérence majeure dans mon projet : il existe plusieurs définitions de l'interface `IDispatcherService` dans différents namespaces. C'est la source de nombreux problèmes de compilation et de logique.

Ta mission est de résoudre ce problème définitivement en suivant ce plan d'action strict :

**Phase 1 : Investigation et Diagnostic**

1.  **Recherche Exhaustive :** Lance une recherche de fichiers pour trouver **toutes** les occurrences de `IDispatcherService.cs` dans l'ensemble du projet. Présente-moi la liste complète des chemins de fichiers trouvés.
2.  **Analyse Comparative :** Lis le contenu de **chaque** fichier `IDispatcherService.cs` trouvé. Présente-moi un tableau comparatif montrant :
    *   Le chemin complet du fichier.
    *   Le namespace de l'interface.
    *   La liste des méthodes et propriétés qu'elle définit.
3.  **Analyse d'Usage :** Pour chaque version de l'interface, identifie quelles classes l'implémentent ou en dépendent (ex: `WpfDispatcherService`, `ClipboardListenerService`, etc.).

**Phase 2 : Plan de Correction (basé sur ta Phase 1)**

Après ton analyse, propose-moi un plan de correction. Ce plan doit être basé sur la décision architecturale suivante :

*   **Règle d'Or :** Il ne doit rester qu'**UNE SEULE et UNIQUE** version de `IDispatcherService.cs`.
*   **Critère de Sélection :** Nous conserverons la version de l'interface qui est la **plus complète et la mieux documentée**.
*   **Emplacement Final :** L'unique fichier `IDispatcherService.cs` doit être situé dans `src/ClipboardPlus/Core/Services/Interfaces/`. Son namespace doit être `ClipboardPlus.Core.Services.Interfaces`.

Ton plan de correction doit inclure les actions suivantes :
1.  La liste des fichiers `IDispatcherService.cs` à **supprimer**.
2.  La confirmation que le fichier `IDispatcherService.cs` final est au bon endroit et a le bon contenu.
3.  La liste de **tous les fichiers de code source** (`.cs`) qui devront être modifiés pour mettre à jour leurs directives `using` et s'aligner sur l'unique interface restante.

**ATTENDS MON ACCORD** après m'avoir présenté ce plan avant de procéder à la moindre modification.

**Phase 3 : Exécution (après mon accord)**

1.  Exécute le plan de correction que nous aurons validé.
2.  Après avoir effectué toutes les suppressions et modifications, lance une **compilation complète de la solution** (`dotnet build`).
3.  Présente-moi le résultat de la compilation. L'objectif est d'atteindre **ZÉRO ERREUR** liée à `IDispatcherService`.

C'est une mission critique pour la santé du projet. Procède avec méthode et rigueur."