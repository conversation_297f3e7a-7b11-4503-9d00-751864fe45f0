using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.LogDeletionResult.Interfaces;
using ClipboardPlus.Core.Services.NewItem.Interfaces;
using ClipboardPlus.Core.Services.Visibility;
using ClipboardPlus.UI.ViewModels.Construction.Implementations;
using ClipboardPlus.UI.ViewModels.Construction.Interfaces;
using Microsoft.Extensions.DependencyInjection;

namespace ClipboardPlus.UI.ViewModels.Construction
{
    /// <summary>
    /// Factory pour la création de ClipboardHistoryViewModel avec injection de dépendances.
    /// Cette factory encapsule la complexité de création du Builder et de ses dépendances.
    /// </summary>
    public static class ClipboardHistoryViewModelFactory
    {
        /// <summary>
        /// Crée une instance de ClipboardHistoryViewModel en utilisant l'injection de dépendances.
        /// Cette méthode est un point d'entrée simplifié qui masque la complexité du Builder pattern.
        /// </summary>
        /// <param name="historyManager">Gestionnaire de l'historique du presse-papiers</param>
        /// <param name="clipboardService">Service d'interaction avec le presse-papiers</param>
        /// <param name="settingsManager">Gestionnaire des paramètres de l'application</param>
        /// <param name="notificationService">Service de notifications utilisateur</param>
        /// <param name="userInteractionService">Service d'interaction utilisateur</param>
        /// <param name="serviceProvider">Fournisseur de services pour l'injection de dépendances</param>
        /// <param name="renameService">Service de renommage des éléments</param>
        /// <param name="deletionLogger">Logger pour les opérations de suppression (optionnel)</param>
        /// <param name="healthService">Service de santé des collections (optionnel)</param>
        /// <param name="visibilityManager">Gestionnaire de visibilité (optionnel)</param>
        /// <param name="newItemOrchestrator">Orchestrateur de création d'éléments (optionnel)</param>
        /// <param name="testDetector">Détecteur d'environnement de test (optionnel)</param>
        /// <returns>Instance configurée de ClipboardHistoryViewModel</returns>
        public static ClipboardHistoryViewModel CreateWithSOLIDArchitecture(
            IClipboardHistoryManager historyManager,
            IClipboardInteractionService clipboardService,
            ISettingsManager settingsManager,
            IUserNotificationService notificationService,
            IUserInteractionService userInteractionService,
            IServiceProvider serviceProvider,
            IRenameService renameService,
            IDeletionResultLogger? deletionLogger = null,
            ICollectionHealthService? healthService = null,
            IVisibilityStateManager? visibilityManager = null,
            INewItemCreationOrchestrator? newItemOrchestrator = null,
            ITestEnvironmentDetector? testDetector = null)
        {
            // Création des services
            var parameterValidator = new ParameterValidator();
            var serviceResolver = new ServiceResolver();
            var eventConfigurationService = new EventConfigurationService();
            var commandInitializer = new CommandInitializer();
            var orchestrationService = new OrchestrationService();

            // Création du Builder avec injection des dépendances
            var builder = new ClipboardHistoryViewModelBuilder(
                parameterValidator,
                serviceResolver,
                eventConfigurationService,
                commandInitializer,
                orchestrationService);

            // Construction du ViewModel avec le Builder fluide
            return builder
                .WithRequiredDependencies(
                    historyManager,
                    clipboardService,
                    settingsManager,
                    notificationService,
                    userInteractionService,
                    serviceProvider,
                    renameService)
                .WithOptionalDependencies(
                    deletionLogger,
                    healthService,
                    visibilityManager,
                    newItemOrchestrator,
                    testDetector,
                    serviceProvider.GetService<ClipboardPlus.Core.Services.Windows.ISettingsWindowService>())
                .Build();
        }

        /// <summary>
        /// Crée une instance de ClipboardHistoryViewModel avec injection de dépendances et initialisation asynchrone.
        /// Cette méthode utilise le nouveau constructeur simplifié + InitializeAsync() pour une approche complète.
        /// Version recommandée pour la nouvelle architecture.
        /// </summary>
        /// <param name="historyManager">Gestionnaire de l'historique du presse-papiers</param>
        /// <param name="clipboardService">Service d'interaction avec le presse-papiers</param>
        /// <param name="settingsManager">Gestionnaire des paramètres de l'application</param>
        /// <param name="notificationService">Service de notifications utilisateur</param>
        /// <param name="userInteractionService">Service d'interaction utilisateur</param>
        /// <param name="serviceProvider">Fournisseur de services pour l'injection de dépendances</param>
        /// <param name="renameService">Service de renommage des éléments</param>
        /// <param name="deletionLogger">Logger pour les opérations de suppression (optionnel)</param>
        /// <param name="healthService">Service de santé des collections (optionnel)</param>
        /// <param name="visibilityManager">Gestionnaire de visibilité (optionnel)</param>
        /// <param name="newItemOrchestrator">Orchestrateur de création d'éléments (optionnel)</param>
        /// <param name="testDetector">Détecteur d'environnement de test (optionnel)</param>
        /// <returns>Instance configurée de ClipboardHistoryViewModel</returns>
        public static async Task<ClipboardHistoryViewModel> CreateWithSOLIDArchitectureAsync(
            IClipboardHistoryManager historyManager,
            IClipboardInteractionService clipboardService,
            ISettingsManager settingsManager,
            IUserNotificationService notificationService,
            IUserInteractionService userInteractionService,
            IServiceProvider serviceProvider,
            IRenameService renameService,
            IDeletionResultLogger? deletionLogger = null,
            ICollectionHealthService? healthService = null,
            IVisibilityStateManager? visibilityManager = null,
            INewItemCreationOrchestrator? newItemOrchestrator = null,
            ITestEnvironmentDetector? testDetector = null)
        {
            // Création des services
            var parameterValidator = new ParameterValidator();
            var serviceResolver = new ServiceResolver();
            var eventConfigurationService = new EventConfigurationService();
            var commandInitializer = new CommandInitializer();
            var orchestrationService = new OrchestrationService();

            // Création du Builder avec injection des dépendances
            var builder = new ClipboardHistoryViewModelBuilder(
                parameterValidator,
                serviceResolver,
                eventConfigurationService,
                commandInitializer,
                orchestrationService);

            // Construction du ViewModel avec le Builder fluide asynchrone
            return await builder
                .WithRequiredDependencies(
                    historyManager,
                    clipboardService,
                    settingsManager,
                    notificationService,
                    userInteractionService,
                    serviceProvider,
                    renameService)
                .WithOptionalDependencies(
                    deletionLogger,
                    healthService,
                    visibilityManager,
                    newItemOrchestrator,
                    testDetector,
                    serviceProvider.GetService<ClipboardPlus.Core.Services.Windows.ISettingsWindowService>())
                .BuildAsync();
        }

        /// <summary>
        /// Crée une instance de ClipboardHistoryViewModel en utilisant l'injection de dépendances.
        /// Cette méthode utilise le ServiceProvider pour résoudre automatiquement le Builder et ses dépendances.
        /// </summary>
        /// <param name="serviceProvider">Fournisseur de services configuré avec l'injection de dépendances</param>
        /// <param name="historyManager">Gestionnaire de l'historique du presse-papiers</param>
        /// <param name="clipboardService">Service d'interaction avec le presse-papiers</param>
        /// <param name="settingsManager">Gestionnaire des paramètres de l'application</param>
        /// <param name="notificationService">Service de notifications utilisateur</param>
        /// <param name="userInteractionService">Service d'interaction utilisateur</param>
        /// <param name="renameService">Service de renommage des éléments</param>
        /// <param name="deletionLogger">Logger pour les opérations de suppression (optionnel)</param>
        /// <param name="healthService">Service de santé des collections (optionnel)</param>
        /// <param name="visibilityManager">Gestionnaire de visibilité (optionnel)</param>
        /// <param name="newItemOrchestrator">Orchestrateur de création d'éléments (optionnel)</param>
        /// <param name="testDetector">Détecteur d'environnement de test (optionnel)</param>
        /// <returns>Instance configurée de ClipboardHistoryViewModel</returns>
        public static ClipboardHistoryViewModel CreateWithDependencyInjection(
            IServiceProvider serviceProvider,
            IClipboardHistoryManager historyManager,
            IClipboardInteractionService clipboardService,
            ISettingsManager settingsManager,
            IUserNotificationService notificationService,
            IUserInteractionService userInteractionService,
            IRenameService renameService,
            IDeletionResultLogger? deletionLogger = null,
            ICollectionHealthService? healthService = null,
            IVisibilityStateManager? visibilityManager = null,
            INewItemCreationOrchestrator? newItemOrchestrator = null,
            ITestEnvironmentDetector? testDetector = null)
        {
            // ✅ ARCHITECTURE MANAGÉRIALE : Essayer d'abord de résoudre les managers
            var historyViewModelManager = serviceProvider.GetService<ClipboardPlus.UI.ViewModels.Managers.Interfaces.IHistoryViewModelManager>();
            var commandViewModelManager = serviceProvider.GetService<ClipboardPlus.UI.ViewModels.Managers.Interfaces.ICommandViewModelManager>();
            var itemCreationManager = serviceProvider.GetService<ClipboardPlus.UI.ViewModels.Managers.Interfaces.IItemCreationManager>();
            var eventViewModelManager = serviceProvider.GetService<ClipboardPlus.UI.ViewModels.Managers.Interfaces.IEventViewModelManager>();
            var visibilityViewModelManager = serviceProvider.GetService<ClipboardPlus.UI.ViewModels.Managers.Interfaces.IVisibilityViewModelManager>();
            var dragDropViewModelManager = serviceProvider.GetService<ClipboardPlus.UI.ViewModels.Managers.Interfaces.IDragDropViewModelManager>();

            // 🚨 DIAGNOSTIC : Vérifier quels managers sont disponibles
            var logger = serviceProvider.GetService<ClipboardPlus.Core.Services.ILoggingService>();
            logger?.LogInfo($"🔍 [DIAGNOSTIC] HistoryViewModelManager: {(historyViewModelManager != null ? "✅ DISPONIBLE" : "❌ NULL")}");
            logger?.LogInfo($"🔍 [DIAGNOSTIC] CommandViewModelManager: {(commandViewModelManager != null ? "✅ DISPONIBLE" : "❌ NULL")}");
            logger?.LogInfo($"🔍 [DIAGNOSTIC] ItemCreationManager: {(itemCreationManager != null ? "✅ DISPONIBLE" : "❌ NULL")}");
            logger?.LogInfo($"🔍 [DIAGNOSTIC] EventViewModelManager: {(eventViewModelManager != null ? "✅ DISPONIBLE" : "❌ NULL")}");
            logger?.LogInfo($"🔍 [DIAGNOSTIC] VisibilityViewModelManager: {(visibilityViewModelManager != null ? "✅ DISPONIBLE" : "❌ NULL")}");
            logger?.LogInfo($"🔍 [DIAGNOSTIC] DragDropViewModelManager: {(dragDropViewModelManager != null ? "✅ DISPONIBLE" : "❌ NULL")}");

            // Si tous les managers sont disponibles, utiliser l'architecture managériale
            if (historyViewModelManager != null && commandViewModelManager != null && itemCreationManager != null &&
                eventViewModelManager != null && visibilityViewModelManager != null && dragDropViewModelManager != null)
            {
                logger?.LogInfo("🎯 [DIAGNOSTIC] UTILISATION DE L'ARCHITECTURE MANAGÉRIALE !");

                var managers = new ManagerDependencies(
                    historyViewModelManager,
                    commandViewModelManager,
                    itemCreationManager,
                    eventViewModelManager,
                    visibilityViewModelManager,
                    dragDropViewModelManager
                );

                var dependencies = new ViewModelDependencies(
                    historyManager,
                    clipboardService,
                    settingsManager,
                    notificationService,
                    userInteractionService,
                    renameService,
                    serviceProvider.GetRequiredService<ClipboardPlus.Modules.History.IHistoryModule>(),
                    serviceProvider.GetRequiredService<ClipboardPlus.Modules.Commands.ICommandModule>(),
                    serviceProvider.GetRequiredService<ClipboardPlus.Modules.Creation.ICreationModule>(),
                    serviceProvider
                );

                logger?.LogInfo("🎯 [DIAGNOSTIC] ClipboardHistoryViewModel créé avec architecture managériale");
                return new ClipboardHistoryViewModel(managers, dependencies);
            }
            else
            {
                logger?.LogInfo("❌ [DIAGNOSTIC] FALLBACK vers ancienne architecture - managers manquants");
            }

            // Fallback vers l'ancienne méthode si les managers ne sont pas disponibles
            var builder = serviceProvider.GetService<IClipboardHistoryViewModelBuilder>();

            if (builder == null)
            {
                throw new InvalidOperationException(
                    "IClipboardHistoryViewModelBuilder n'est pas configuré dans l'injection de dépendances. " +
                    "Utilisez CreateWithSOLIDArchitecture() ou configurez les services dans HostConfiguration.");
            }

            // Construction du ViewModel avec le Builder résolu (pattern fluide)
            // CORRECTION : Utiliser BuildAsync() pour que InitializeAsync() soit appelée
            var buildTask = builder
                .WithRequiredDependencies(
                    historyManager,
                    clipboardService,
                    settingsManager,
                    notificationService,
                    userInteractionService,
                    serviceProvider,
                    renameService)
                .WithOptionalDependencies(
                    deletionLogger,
                    healthService,
                    visibilityManager,
                    newItemOrchestrator,
                    testDetector,
                    serviceProvider.GetService<ClipboardPlus.Core.Services.Windows.ISettingsWindowService>())
                .BuildAsync();

            // Attendre le résultat de manière synchrone pour l'injection de dépendances
            return buildTask.GetAwaiter().GetResult();
        }

        /// <summary>
        /// Crée une instance de ClipboardHistoryViewModel en utilisant un DTO de dépendances.
        /// Cette méthode simplifie l'utilisation de la Factory avec le nouveau constructeur DTO.
        /// </summary>
        /// <param name="dependencies">DTO contenant toutes les dépendances obligatoires</param>
        /// <param name="optionalServices">DTO contenant les services optionnels (optionnel)</param>
        /// <returns>Instance configurée de ClipboardHistoryViewModel</returns>
        public static ClipboardHistoryViewModel Create(
            ViewModelDependencies dependencies,
            OptionalServicesDependencies? optionalServices = null)
        {
            return CreateWithSOLIDArchitecture(
                dependencies.ClipboardHistoryManager,
                dependencies.ClipboardInteractionService,
                dependencies.SettingsManager,
                dependencies.UserNotificationService,
                dependencies.UserInteractionService,
                dependencies.ServiceProvider,
                dependencies.RenameService,
                optionalServices?.DeletionResultLogger,
                optionalServices?.CollectionHealthService,
                optionalServices?.VisibilityStateManager,
                optionalServices?.NewItemCreationOrchestrator,
                optionalServices?.TestEnvironmentDetector);
        }
    }
}
