using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Input;
using System.Windows.Threading;
using System.Threading.Tasks;
using CommunityToolkit.Mvvm.Input;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.SupprimerTout;
using ClipboardPlus.Core.Services.Visibility;
using ClipboardPlus.Core.Services.LogDeletionResult.Interfaces;
using ClipboardPlus.Core.Services.LogDeletionResult.Models;
using ClipboardPlus.Core.Services.Deletion;
using ClipboardPlus.Core.Services.NewItem.Interfaces;
using ClipboardPlus.Core.Services.Windows;
using Microsoft.Extensions.DependencyInjection;
using ClipboardPlus.Utils;
using ClipboardPlus.UI.ViewModels.Construction.Implementations;
using GongSolutions.Wpf.DragDrop;
using ClipboardPlus.UI.Windows;
using ClipboardPlus.UI.Controls;
using ClipboardPlus.UI.Helpers;
using WpfDragDrop = GongSolutions.Wpf.DragDrop;
using WpfMessageBox = System.Windows.MessageBox;
using WpfDragDropEffects = System.Windows.DragDropEffects;
using WpfApplication = System.Windows.Application;
using WpfDropTargetAdorners = GongSolutions.Wpf.DragDrop.DropTargetAdorners;
using WpfMessageBoxResult = System.Windows.MessageBoxResult;
using ClipboardPlus.Core.Extensions;
using System.Diagnostics;
using ClipboardPlus.Diagnostics;
using System.Threading;
using ClipboardPlus.UI.Native;
using CommunityToolkit.Mvvm.Messaging;
using ClipboardPlus.UI.Messages;
using ClipboardPlus.UI.ViewModels.Managers.Interfaces;

namespace ClipboardPlus.UI.ViewModels
{
    /// <summary>
    /// DTO pour encapsuler toutes les dépendances obligatoires du ClipboardHistoryViewModel.
    /// Réduit le nombre de paramètres du constructeur de 13 à 2.
    /// </summary>
    public record ViewModelDependencies(
        // === SERVICES CORE (6 obligatoires) ===
        IClipboardHistoryManager ClipboardHistoryManager,
        IClipboardInteractionService ClipboardInteractionService,
        ISettingsManager SettingsManager,
        IUserNotificationService UserNotificationService,
        IUserInteractionService UserInteractionService,
        IRenameService RenameService,

        // === MODULES ARCHITECTURE (3 nouveaux) ===
        ClipboardPlus.Modules.History.IHistoryModule HistoryModule,
        ClipboardPlus.Modules.Commands.ICommandModule CommandModule,
        ClipboardPlus.Modules.Creation.ICreationModule CreationModule,

        // === INFRASTRUCTURE (1 obligatoire) ===
        IServiceProvider ServiceProvider
    );

    /// <summary>
    /// DTO pour encapsuler les services optionnels du ClipboardHistoryViewModel.
    /// Sépare les dépendances obligatoires des optionnelles pour plus de clarté.
    /// </summary>
    public record OptionalServicesDependencies(
        IDeletionResultLogger? DeletionResultLogger = null,
        ICollectionHealthService? CollectionHealthService = null,
        IVisibilityStateManager? VisibilityStateManager = null,
        INewItemCreationOrchestrator? NewItemCreationOrchestrator = null,
        ITestEnvironmentDetector? TestEnvironmentDetector = null,
        ClipboardPlus.Core.Services.Windows.ISettingsWindowService? SettingsWindowService = null,
        bool SkipCommandInitialization = false
    );

    /// <summary>
    /// DTO pour encapsuler les 6 managers spécialisés du ClipboardHistoryViewModel.
    /// Architecture managériale moderne remplaçant l'architecture fragmentée des fichiers partiels.
    /// </summary>
    public record ManagerDependencies(
        IHistoryViewModelManager HistoryManager,
        ICommandViewModelManager CommandManager,
        IItemCreationManager ItemCreationManager,
        IEventViewModelManager EventManager,
        IVisibilityViewModelManager VisibilityManager,
        IDragDropViewModelManager DragDropManager
    );

    /// <summary>
    /// ViewModel pour la fenêtre d'historique du presse-papiers.
    /// </summary>
    public partial class ClipboardHistoryViewModel : ViewModelBase, WpfDragDrop.IDropTarget, IDisposable, IViewModelOperationState
    {
        // Commandes pour le renommage - PHASE 4 : Délégation vers ItemCreationManager
        public IRelayCommand<ClipboardItem> DemarrerRenommageCommand
        {
            get
            {
                // PHASE 4 : Déléguer vers le manager si disponible
                if (IsManagerArchitectureAvailable && _itemCreationManager != null)
                {
                    return _itemCreationManager.DemarrerRenommageCommand as IRelayCommand<ClipboardItem> ?? _legacyDemarrerRenommageCommand;
                }

                // Fallback vers l'implémentation existante
                return _legacyDemarrerRenommageCommand;
            }
            private set => _legacyDemarrerRenommageCommand = value;
        }

        public IRelayCommand ConfirmerRenommageCommand
        {
            get
            {
                // PHASE 4 : Déléguer vers le manager si disponible
                if (IsManagerArchitectureAvailable && _itemCreationManager != null)
                {
                    return _itemCreationManager.ConfirmerRenommageCommand as IRelayCommand ?? _legacyConfirmerRenommageCommand;
                }

                // Fallback vers l'implémentation existante
                return _legacyConfirmerRenommageCommand;
            }
            private set => _legacyConfirmerRenommageCommand = value;
        }

        public IRelayCommand AnnulerRenommageCommand
        {
            get
            {
                // PHASE 4 : Déléguer vers le manager si disponible
                if (IsManagerArchitectureAvailable && _itemCreationManager != null)
                {
                    return _itemCreationManager.AnnulerRenommageCommand as IRelayCommand ?? _legacyAnnulerRenommageCommand;
                }

                // Fallback vers l'implémentation existante
                return _legacyAnnulerRenommageCommand;
            }
            private set => _legacyAnnulerRenommageCommand = value;
        }

        // Commandes pour la création de nouveaux éléments - PHASE 4 : Délégation vers ItemCreationManager
        public IRelayCommand PrepareNewItemCommand
        {
            get
            {
                // PHASE 4 : Déléguer vers le manager si disponible
                if (IsManagerArchitectureAvailable && _itemCreationManager != null)
                {
                    return _itemCreationManager.PrepareNewItemCommand as IRelayCommand ?? _legacyPrepareNewItemCommand;
                }

                // Fallback vers l'implémentation existante
                return _legacyPrepareNewItemCommand;
            }
            private set => _legacyPrepareNewItemCommand = value;
        }

        public IRelayCommand FinalizeAndSaveNewItemCommand
        {
            get
            {
                // PHASE 4 : Déléguer vers le manager si disponible
                if (IsManagerArchitectureAvailable && _itemCreationManager != null)
                {
                    return _itemCreationManager.FinalizeAndSaveNewItemCommand as IRelayCommand ?? _legacyFinalizeAndSaveNewItemCommand;
                }

                // Fallback vers l'implémentation existante
                return _legacyFinalizeAndSaveNewItemCommand;
            }
            private set => _legacyFinalizeAndSaveNewItemCommand = value;
        }

        public IRelayCommand DiscardNewItemCreationCommand
        {
            get
            {
                // PHASE 4 : Déléguer vers le manager si disponible
                if (IsManagerArchitectureAvailable && _itemCreationManager != null)
                {
                    return _itemCreationManager.DiscardNewItemCreationCommand as IRelayCommand ?? _legacyDiscardNewItemCreationCommand;
                }

                // Fallback vers l'implémentation existante
                return _legacyDiscardNewItemCreationCommand;
            }
            private set => _legacyDiscardNewItemCreationCommand = value;
        }

        // Commandes restantes - PHASE 4 : Délégation vers CommandModule/CommandViewModelManager
        public IRelayCommand PasteSelectedItemCommand
        {
            get
            {
                // PHASE 4 : Déléguer vers le manager si disponible
                if (IsManagerArchitectureAvailable && _commandManager != null)
                {
                    return _commandManager.PasteSelectedItemCommand;
                }

                // Fallback vers l'implémentation existante
                return _legacyPasteSelectedItemCommand;
            }
            private set => _legacyPasteSelectedItemCommand = value;
        }

        public IAsyncRelayCommand<ClipboardItem> BasculerEpinglageCommand
        {
            get
            {
                // PHASE 4 : Déléguer vers le manager si disponible
                if (IsManagerArchitectureAvailable && _commandManager != null)
                {
                    return _commandManager.BasculerEpinglageCommand;
                }

                // Fallback vers l'implémentation existante
                return _legacyBasculerEpinglageCommand;
            }
            private set => _legacyBasculerEpinglageCommand = value;
        }

        public IRelayCommand<ClipboardItem> SupprimerElementCommand
        {
            get
            {
                // PHASE 4 : Déléguer vers le manager si disponible
                if (IsManagerArchitectureAvailable && _commandManager != null)
                {
                    return _commandManager.SupprimerElementCommand;
                }

                // Fallback vers l'implémentation existante
                return _legacySupprimerElementCommand;
            }
            private set => _legacySupprimerElementCommand = value;
        }

        public IRelayCommand<ClipboardItem> SupprimerElementCommand_V2
        {
            get
            {
                // PHASE 4 : Déléguer vers le manager si disponible
                if (IsManagerArchitectureAvailable && _commandManager != null)
                {
                    return _commandManager.SupprimerElementCommand_V2;
                }

                // Fallback vers l'implémentation existante
                return _legacySupprimerElementCommand_V2;
            }
            private set => _legacySupprimerElementCommand_V2 = value;
        }

        public IAsyncRelayCommand SupprimerToutCommand
        {
            get
            {
                // PHASE 4 : Déléguer vers le manager si disponible
                if (IsManagerArchitectureAvailable && _commandManager != null)
                {
                    return _commandManager.SupprimerToutCommand;
                }

                // Fallback vers l'implémentation existante
                return _legacySupprimerToutCommand;
            }
            private set => _legacySupprimerToutCommand = value;
        }

        public IRelayCommand<ClipboardItem> AfficherPreviewCommand
        {
            get
            {
                // PHASE 4 : Déléguer vers le manager si disponible
                if (IsManagerArchitectureAvailable && _commandManager != null)
                {
                    return _commandManager.AfficherPreviewCommand;
                }

                // Fallback vers l'implémentation existante
                return _legacyAfficherPreviewCommand;
            }
            private set => _legacyAfficherPreviewCommand = value;
        }

        // Commande pour le nettoyage avancé - PHASE 4 : Délégation vers CommandViewModelManager
        public IAsyncRelayCommand OpenAdvancedCleanupCommand
        {
            get
            {
                // PHASE 4 : Déléguer vers le manager si disponible
                if (IsManagerArchitectureAvailable && _commandManager != null)
                {
                    return _commandManager.OpenAdvancedCleanupCommand;
                }

                // Fallback vers l'implémentation existante
                return _legacyOpenAdvancedCleanupCommand;
            }
            private set => _legacyOpenAdvancedCleanupCommand = value;
        }

        // Commande pour ouvrir les paramètres - PHASE 4 : Délégation vers CommandViewModelManager
        public IAsyncRelayCommand OpenSettingsCommand
        {
            get
            {
                // PHASE 4 : Déléguer vers le manager si disponible
                if (IsManagerArchitectureAvailable && _commandManager != null)
                {
                    return _commandManager.OpenSettingsCommand;
                }

                // Fallback vers l'implémentation existante
                return _legacyOpenSettingsCommand;
            }
            private set => _legacyOpenSettingsCommand = value;
        }

        private readonly IClipboardHistoryManager _clipboardHistoryManager;
        private readonly ISettingsManager _settingsManager;
        private readonly ILoggingService? _loggingService; // Conservé car utilisé potentiellement avant l'initialisation des helpers
        private readonly IDeletionDiagnostic? _deletionDiagnostic; // Conservé pour la même raison
        private readonly IDeletionResultLogger? _deletionResultLogger; // Nouveau système de logging de suppression
        private readonly HistoryCollectionSynchronizer _historyCollectionSynchronizer;
        private readonly IClipboardInteractionService _clipboardInteractionService;
        private readonly IUserNotificationService _userNotificationService;
        private readonly IUserInteractionService _userInteractionService;
        private readonly IServiceProvider _serviceProvider;
        private readonly IRenameService _renameService;
        private readonly ICollectionHealthService? _collectionHealthService;

        // === MODULES ARCHITECTURE OPTIMISÉE ===
        private readonly ClipboardPlus.Modules.History.IHistoryModule _historyModule;
        private readonly ClipboardPlus.Modules.Commands.ICommandModule _commandModule;
        private readonly ClipboardPlus.Modules.Creation.ICreationModule _creationModule;

        // === MANAGERS ARCHITECTURE NOUVELLE (PHASE 4) ===
        private readonly IHistoryViewModelManager? _historyManager;
        private readonly ICommandViewModelManager? _commandManager;
        private readonly IItemCreationManager? _itemCreationManager;
        private readonly IEventViewModelManager? _eventManager;
        private readonly IVisibilityViewModelManager? _visibilityManager;
        private readonly IDragDropViewModelManager? _dragDropManager;

        // === COMMANDES LEGACY POUR FALLBACK ===
        private IRelayCommand _legacyPasteSelectedItemCommand = null!;
        private IAsyncRelayCommand<ClipboardItem> _legacyBasculerEpinglageCommand = null!;
        private IRelayCommand<ClipboardItem> _legacySupprimerElementCommand = null!;
        private IRelayCommand<ClipboardItem> _legacySupprimerElementCommand_V2 = null!;
        private IAsyncRelayCommand _legacySupprimerToutCommand = null!;
        private IRelayCommand<ClipboardItem> _legacyAfficherPreviewCommand = null!;
        private IAsyncRelayCommand _legacyOpenAdvancedCleanupCommand = null!;
        private IAsyncRelayCommand _legacyOpenSettingsCommand = null!;

        // === COMMANDES LEGACY POUR ITEMCREATIONMANAGER FALLBACK ===
        private IRelayCommand<ClipboardItem> _legacyDemarrerRenommageCommand = null!;
        private IRelayCommand _legacyConfirmerRenommageCommand = null!;
        private IRelayCommand _legacyAnnulerRenommageCommand = null!;
        private IRelayCommand _legacyPrepareNewItemCommand = null!;
        private IRelayCommand _legacyFinalizeAndSaveNewItemCommand = null!;
        private IRelayCommand _legacyDiscardNewItemCreationCommand = null!;

        /// <summary>
        /// OPTIMISATION : Validation rapide de la disponibilité des modules
        /// Évite les vérifications répétées dans chaque méthode
        /// </summary>
        private bool IsModularArchitectureAvailable =>
            _historyModule != null && _commandModule != null && _creationModule != null;

        /// <summary>
        /// PHASE 4 : Validation rapide de la disponibilité de l'architecture managériale
        /// Évite les vérifications répétées dans chaque méthode
        /// RÉACTIVÉ : Problèmes de types de commandes résolus avec le wrapping pattern.
        /// </summary>
        private bool IsManagerArchitectureAvailable =>
            _historyManager != null && _commandManager != null && _itemCreationManager != null &&
            _eventManager != null && _visibilityManager != null && _dragDropManager != null;

        /// <summary>
        /// Propriété publique pour les tests d'intégration.
        /// Permet de valider que l'architecture managériale est correctement activée.
        /// </summary>
        public bool IsManagerArchitectureActive => IsManagerArchitectureAvailable;

        /// <summary>
        /// Statistiques d'utilisation des modules pour monitoring et optimisation
        /// </summary>
        private readonly Dictionary<string, ModuleUsageStats> _moduleUsageStats = new();

        private class ModuleUsageStats
        {
            public int ModuleSuccessCount { get; set; }
            public DateTime LastModuleSuccess { get; set; }
        }

        // === SERVICES INJECTÉS ===
        private readonly IVisibilityStateManager? _visibilityStateManager;
        private readonly INewItemCreationOrchestrator? _newItemCreationOrchestrator;
        private readonly ITestEnvironmentDetector? _testEnvironmentDetector;
        private readonly ClipboardPlus.Core.Services.Windows.ISettingsWindowService? _settingsWindowService;

        // === SERVICES POUR REFACTORISATION SUPPRIMERELEMENT V2 ===
        private readonly IDeletionService? _deletionService;
        private readonly IDeletionUIValidator? _deletionUIValidator;
        private readonly IDeletionUIHandler? _deletionUIHandler;
        private readonly IDeletionUINotificationService? _deletionUINotificationService;

        // Services optionnels
        private IHistoryChangeOrchestrator? _historyChangeOrchestrator;
        private IFeatureFlagService? _featureFlagService;
        private ClipboardItem? _selectedClipboardItem;
        private string _searchText = string.Empty;
        private bool _isLoading = false;
        private bool _isInitialized = false;

        private bool _isOperationInProgress = false;
        internal bool _isItemPasteInProgress = false;
        private bool _isReorderingItems = false;
        private bool _hideItemTitle = false;

        // === CHAMPS PRIVÉS POUR ITEMCREATIONMANAGER FALLBACK ===
        private string _newItemTextContent = string.Empty;
        private bool _isItemCreationActive = false;
        private ClipboardItem? _itemEnRenommage;
        private string _nouveauNom = string.Empty;

        // === ÉVÉNEMENTS LEGACY POUR EVENTVIEWMODELMANAGER FALLBACK ===
        private EventHandler? _legacyRequestCloseDialog;

        // === CHAMPS PRIVÉS POUR EVENTVIEWMODELMANAGER FALLBACK ===
        private bool _isEventHandlingActive = true;
        private int _processedEventCount = 0;

        // === CHAMPS PRIVÉS POUR VISIBILITYVIEWMODELMANAGER FALLBACK ===
        private bool _legacyHideTimestamp = false;
        private bool _legacyHideItemTitle = false;
        private bool _showContentTypeIcons = true;
        private bool _showPinIndicators = true;
        private bool _showImagePreviews = true;

        // === CHAMPS PRIVÉS POUR DRAGDROPVIEWMODELMANAGER FALLBACK ===
        private bool _isDragDropActive = false;
        private ClipboardDataType? _currentDragDataType = null;
        private bool _isDropAllowed = false;

        // Déclaration de l'événement pour demander la fermeture du dialogue
        // PHASE 4 : Délégation vers EventViewModelManager
        public event EventHandler? RequestCloseDialog
        {
            add
            {
                // PHASE 4 : Déléguer vers le manager si disponible
                if (IsManagerArchitectureAvailable && _eventManager != null)
                {
                    // L'EventViewModelManager orchestre cet événement
                    _eventManager.RequestCloseDialog += value;
                }
                else
                {
                    // Fallback vers l'implémentation existante
                    _legacyRequestCloseDialog += value;
                }
            }
            remove
            {
                // PHASE 4 : Déléguer vers le manager si disponible
                if (IsManagerArchitectureAvailable && _eventManager != null)
                {
                    _eventManager.RequestCloseDialog -= value;
                }
                else
                {
                    // Fallback vers l'implémentation existante
                    _legacyRequestCloseDialog -= value;
                }
            }
        }

        /// <summary>
        /// Déclenche l'événement RequestCloseDialog.
        /// PHASE 4 : Délégation vers EventViewModelManager
        /// </summary>
        protected virtual void OnRequestCloseDialog()
        {
            // PHASE 4 : Déléguer vers le manager si disponible
            if (IsManagerArchitectureAvailable && _eventManager != null)
            {
                _eventManager.TriggerRequestCloseDialog();
            }
            else
            {
                // Fallback vers l'implémentation existante
                _legacyRequestCloseDialog?.Invoke(this, EventArgs.Empty);
            }
        }

        /// <summary>
        /// PHASE 4 : Enregistre l'utilisation réussie d'un module
        /// </summary>
        private void RecordModuleSuccess(string methodName)
        {
            if (!_moduleUsageStats.ContainsKey(methodName))
            {
                _moduleUsageStats[methodName] = new ModuleUsageStats();
            }

            _moduleUsageStats[methodName].ModuleSuccessCount++;
            _moduleUsageStats[methodName].LastModuleSuccess = DateTime.Now;

            _loggingService?.LogDebug($"[STATS] {methodName} - Module success #{_moduleUsageStats[methodName].ModuleSuccessCount}");
        }



        /// <summary>
        /// PHASE 4 : Analyse les statistiques d'utilisation pour déterminer les méthodes stables
        /// </summary>
        public void AnalyzeModuleUsage()
        {
            _loggingService?.LogInfo("=== ANALYSE UTILISATION MODULES ===");

            foreach (var kvp in _moduleUsageStats)
            {
                var methodName = kvp.Key;
                var stats = kvp.Value;

                _loggingService?.LogInfo($"{methodName}:");
                _loggingService?.LogInfo($"  - Succès modules: {stats.ModuleSuccessCount}");
                _loggingService?.LogInfo($"  - Dernière utilisation: {stats.LastModuleSuccess}");
            }

            _loggingService?.LogInfo("=== FIN ANALYSE ===");
        }





        /// <summary>
        /// PHASE 5 : Système de benchmark pour mesurer les performances
        /// </summary>
        private readonly Dictionary<string, List<long>> _performanceMetrics = new();

        /// <summary>
        /// PHASE 5 : Enregistre une métrique de performance
        /// </summary>
        private void RecordPerformanceMetric(string operation, long elapsedMilliseconds)
        {
            if (!_performanceMetrics.ContainsKey(operation))
            {
                _performanceMetrics[operation] = new List<long>();
            }

            _performanceMetrics[operation].Add(elapsedMilliseconds);

            // Garder seulement les 100 dernières mesures
            if (_performanceMetrics[operation].Count > 100)
            {
                _performanceMetrics[operation].RemoveAt(0);
            }
        }

        /// <summary>
        /// PHASE 5 : Génère un rapport de performance
        /// </summary>
        public void GeneratePerformanceReport()
        {
            _loggingService?.LogInfo("=== RAPPORT DE PERFORMANCE ===");

            foreach (var kvp in _performanceMetrics)
            {
                var operation = kvp.Key;
                var metrics = kvp.Value;

                if (metrics.Count > 0)
                {
                    var average = metrics.Average();
                    var min = metrics.Min();
                    var max = metrics.Max();

                    _loggingService?.LogInfo($"{operation}:");
                    _loggingService?.LogInfo($"  - Moyenne: {average:F2}ms");
                    _loggingService?.LogInfo($"  - Min: {min}ms");
                    _loggingService?.LogInfo($"  - Max: {max}ms");
                    _loggingService?.LogInfo($"  - Échantillons: {metrics.Count}");
                }
            }

            _loggingService?.LogInfo("=== FIN RAPPORT ===");
        }

        /// <summary>
        /// Indique si une opération de collage d'élément est en cours.
        /// Propriété interne pour l'accès par HistoryCollectionSynchronizer.
        /// </summary>
        internal bool IsItemPasteInProgress { get => _isItemPasteInProgress; set => _isItemPasteInProgress = value; }

        /// <summary>
        /// Collection des éléments de l'historique du presse-papiers.
        /// PHASE 4 : Délégation vers HistoryViewModelManager si disponible.
        /// </summary>
        public ObservableCollection<ClipboardItem> HistoryItems
        {
            get
            {
                // PHASE 4 : Déléguer vers le manager si disponible
                if (IsManagerArchitectureAvailable && _historyManager != null)
                {
                    return _historyManager.HistoryItems;
                }

                // Fallback vers l'implémentation existante
                return _legacyHistoryItems;
            }
        }

        // Collection legacy pour compatibilité
        private readonly ObservableCollection<ClipboardItem> _legacyHistoryItems = new ObservableCollection<ClipboardItem>();

        /// <summary>
        /// Élément de l'historique sélectionné.
        /// PHASE 4 : Délégation vers HistoryViewModelManager si disponible.
        /// </summary>
        public ClipboardItem? SelectedClipboardItem
        {
            get
            {
                // PHASE 4 : Déléguer vers le manager si disponible
                if (IsManagerArchitectureAvailable && _historyManager != null)
                {
                    return _historyManager.SelectedClipboardItem;
                }

                // Fallback vers l'implémentation existante
                return _selectedClipboardItem;
            }
            set
            {
                // PHASE 4 : Déléguer vers le manager si disponible
                if (IsManagerArchitectureAvailable && _historyManager != null)
                {
                    _historyManager.SelectedClipboardItem = value;
                }
                else
                {
                    // Fallback vers l'implémentation existante
                    SetProperty(ref _selectedClipboardItem, value);
                }
            }
        }

        /// <summary>
        /// Texte de recherche pour filtrer les éléments.
        /// PHASE 4 : Délégation vers HistoryViewModelManager si disponible.
        /// </summary>
        public string SearchText
        {
            get
            {
                // PHASE 4 : Déléguer vers le manager si disponible
                if (IsManagerArchitectureAvailable && _historyManager != null)
                {
                    return _historyManager.SearchText;
                }

                // Fallback vers l'implémentation existante
                return _searchText;
            }
            set
            {
                // PHASE 4 : Déléguer vers le manager si disponible
                if (IsManagerArchitectureAvailable && _historyManager != null)
                {
                    _historyManager.SearchText = value;
                }
                else
                {
                    // Fallback vers l'implémentation existante
                    if (SetProperty(ref _searchText, value))
                    {
                        ApplySearchFilterUsingModule(value);
                        RecordModuleSuccess("ApplySearchFilter");
                    }
                }
            }
        }

        /// <summary>
        /// Indique si l'historique est en cours de chargement.
        /// PHASE 4 : Délégation vers HistoryViewModelManager si disponible.
        /// </summary>
        public bool IsLoading
        {
            get
            {
                // PHASE 4 : Déléguer vers le manager si disponible
                if (IsManagerArchitectureAvailable && _historyManager != null)
                {
                    return _historyManager.IsLoading;
                }

                // Fallback vers l'implémentation existante
                return _isLoading;
            }
            set
            {
                // PHASE 4 : Le manager gère IsLoading en interne, pas d'assignation directe
                if (IsManagerArchitectureAvailable && _historyManager != null)
                {
                    // Le manager gère IsLoading automatiquement lors des opérations
                    // Pas d'assignation directe nécessaire
                    return;
                }

                // Fallback vers l'implémentation existante
                SetProperty(ref _isLoading, value);
            }
        }

        /// <summary>
        /// Indique si l'initialisation asynchrone (InitializeAsync) a été effectuée.
        /// Cette propriété permet de détecter la régression où InitializeAsync() n'était pas appelée
        /// dans CreateWithDependencyInjection(), empêchant les abonnements aux messages.
        /// </summary>
        public bool IsInitialized
        {
            get => _isInitialized;
            private set => SetProperty(ref _isInitialized, value);
        }

        /// <summary>
        /// Obtient ou définit une valeur indiquant si une opération comme une suppression est en cours.
        /// Cette propriété est utilisée pour éviter de fermer la fenêtre principale pendant les opérations.
        /// Le setter est public pour permettre au DeletionService de gérer l'état.
        /// </summary>
        public bool IsOperationInProgress
        {
            get => _isOperationInProgress;
            set // Setter public - DeletionService gère l'état
            {
                if (SetProperty(ref _isOperationInProgress, value))
                {
                    // SupprimerElementCommand?.NotifyCanExecuteChanged();
                    // BasculerEpinglageCommand?.NotifyCanExecuteChanged();
                    // AfficherPreviewCommand?.NotifyCanExecuteChanged();
                    // PasteSelectedItemCommand?.NotifyCanExecuteChanged();
                    Debug.WriteLine($"[IsOperationInProgress] Changement d'état: {value}, Commandes notifiées, Heure: {DateTime.Now:HH:mm:ss.fff}");
                }
            }
        }

        /// <summary>
        /// Indique si l'horodatage doit être masqué dans l'interface.
        /// PHASE 4 : Délégation vers VisibilityViewModelManager
        /// </summary>
        public bool HideTimestamp
        {
            get
            {
                // PHASE 4 : Déléguer vers le manager si disponible
                if (IsManagerArchitectureAvailable && _visibilityManager != null)
                {
                    return !_visibilityManager.ShowTimestamps; // Inversion logique : Hide = !Show
                }

                // Fallback vers l'implémentation existante
                return _legacyHideTimestamp;
            }
            private set
            {
                // PHASE 4 : Déléguer vers le manager si disponible
                if (IsManagerArchitectureAvailable && _visibilityManager != null)
                {
                    _visibilityManager.ShowTimestamps = !value; // Inversion logique : Hide = !Show
                }
                else
                {
                    // Fallback vers l'implémentation existante
                    SetProperty(ref _legacyHideTimestamp, value);
                }
            }
        }

        /// <summary>
        /// Contenu textuel pour le nouvel élément en cours de création.
        /// PHASE 4 : Délégation vers ItemCreationManager
        /// </summary>
        public string NewItemTextContent
        {
            get
            {
                // PHASE 4 : Déléguer vers le manager si disponible
                if (IsManagerArchitectureAvailable && _itemCreationManager != null)
                {
                    return _itemCreationManager.NewItemTextContent ?? string.Empty;
                }

                // Fallback vers l'implémentation existante
                return _newItemTextContent;
            }
            set
            {
                // PHASE 4 : Déléguer vers le manager si disponible
                if (IsManagerArchitectureAvailable && _itemCreationManager != null)
                {
                    _itemCreationManager.NewItemTextContent = value;
                }
                else
                {
                    // Fallback vers l'implémentation existante
                    SetProperty(ref _newItemTextContent, value);
                    FinalizeAndSaveNewItemCommand?.NotifyCanExecuteChanged();
                }
            }
        }

        /// <summary>
        /// Indique si l'éditeur de création d'élément est actif.
        /// PHASE 4 : Délégation vers ItemCreationManager
        /// </summary>
        public bool IsItemCreationActive
        {
            get
            {
                // PHASE 4 : Déléguer vers le manager si disponible
                if (IsManagerArchitectureAvailable && _itemCreationManager != null)
                {
                    return _itemCreationManager.IsItemCreationActive;
                }

                // Fallback vers l'implémentation existante
                return _isItemCreationActive;
            }
            set
            {
                // PHASE 4 : Déléguer vers le manager si disponible
                if (IsManagerArchitectureAvailable && _itemCreationManager != null)
                {
                    _itemCreationManager.IsItemCreationActive = value;
                }
                else
                {
                    // Fallback vers l'implémentation existante
                    SetProperty(ref _isItemCreationActive, value);
                }
            }
        }

        /// <summary>
        /// Indique si les titres des éléments doivent être masqués dans l'interface.
        /// PHASE 4 : Délégation vers VisibilityViewModelManager
        /// </summary>
        public bool HideItemTitle
        {
            get
            {
                // PHASE 4 : Déléguer vers le manager si disponible
                if (IsManagerArchitectureAvailable && _visibilityManager != null)
                {
                    return !_visibilityManager.ShowTitles; // Inversion logique : Hide = !Show
                }

                // Fallback vers l'implémentation existante
                return _hideItemTitle;
            }
            private set
            {
                // PHASE 4 : Déléguer vers le manager si disponible
                if (IsManagerArchitectureAvailable && _visibilityManager != null)
                {
                    _visibilityManager.ShowTitles = !value; // Inversion logique : Hide = !Show
                }
                else
                {
                    // Fallback vers l'implémentation existante
                    SetProperty(ref _hideItemTitle, value);
                }
            }
        }

        /// <summary>
        /// Élément actuellement en cours de renommage.
        /// PHASE 4 : Délégation vers ItemCreationManager
        /// </summary>
        public ClipboardItem? ItemEnRenommage
        {
            get
            {
                // PHASE 4 : Déléguer vers le manager si disponible
                if (IsManagerArchitectureAvailable && _itemCreationManager != null)
                {
                    return _itemCreationManager.ItemEnRenommage;
                }

                // Fallback vers l'implémentation existante
                return _itemEnRenommage;
            }
            set
            {
                // PHASE 4 : Déléguer vers le manager si disponible
                if (IsManagerArchitectureAvailable && _itemCreationManager != null)
                {
                    _itemCreationManager.ItemEnRenommage = value;
                }
                else
                {
                    // Fallback vers l'implémentation existante
                    if (SetProperty(ref _itemEnRenommage, value))
                    {
                        // Mettre à jour les commandes qui dépendent de cette propriété
                        ConfirmerRenommageCommand?.NotifyCanExecuteChanged();
                        AnnulerRenommageCommand?.NotifyCanExecuteChanged();
                        DemarrerRenommageCommand?.NotifyCanExecuteChanged();
                    }
                }
            }
        }

        /// <summary>
        /// Nouveau nom pour l'élément en cours de renommage.
        /// PHASE 4 : Délégation vers ItemCreationManager
        /// </summary>
        public string NouveauNom
        {
            get
            {
                // PHASE 4 : Déléguer vers le manager si disponible
                if (IsManagerArchitectureAvailable && _itemCreationManager != null)
                {
                    return _itemCreationManager.NouveauNom ?? string.Empty;
                }

                // Fallback vers l'implémentation existante
                return _nouveauNom;
            }
            set
            {
                // PHASE 4 : Déléguer vers le manager si disponible
                if (IsManagerArchitectureAvailable && _itemCreationManager != null)
                {
                    _itemCreationManager.NouveauNom = value;
                }
                else
                {
                    // Fallback vers l'implémentation existante
                    SetProperty(ref _nouveauNom, value);
                }
            }
        }

        /// <summary>
        /// Indique si la gestion des événements est active.
        /// PHASE 4 : Délégation vers EventViewModelManager
        /// </summary>
        public bool IsEventHandlingActive
        {
            get
            {
                // PHASE 4 : Déléguer vers le manager si disponible
                if (IsManagerArchitectureAvailable && _eventManager != null)
                {
                    return _eventManager.IsEventHandlingActive;
                }

                // Fallback vers l'implémentation existante
                return _isEventHandlingActive;
            }
            set
            {
                // PHASE 4 : Déléguer vers le manager si disponible
                if (IsManagerArchitectureAvailable && _eventManager != null)
                {
                    _eventManager.IsEventHandlingActive = value;
                }
                else
                {
                    // Fallback vers l'implémentation existante
                    SetProperty(ref _isEventHandlingActive, value);
                }
            }
        }

        /// <summary>
        /// Nombre d'événements traités depuis le démarrage.
        /// PHASE 4 : Délégation vers EventViewModelManager
        /// </summary>
        public int ProcessedEventCount
        {
            get
            {
                // PHASE 4 : Déléguer vers le manager si disponible
                if (IsManagerArchitectureAvailable && _eventManager != null)
                {
                    return _eventManager.ProcessedEventCount;
                }

                // Fallback vers l'implémentation existante
                return _processedEventCount;
            }
        }

        /// <summary>
        /// Indique si les icônes de type de contenu doivent être affichées.
        /// PHASE 4 : Délégation vers VisibilityViewModelManager
        /// </summary>
        public bool ShowContentTypeIcons
        {
            get
            {
                // PHASE 4 : Déléguer vers le manager si disponible
                if (IsManagerArchitectureAvailable && _visibilityManager != null)
                {
                    return _visibilityManager.ShowContentTypeIcons;
                }

                // Fallback vers l'implémentation existante
                return _showContentTypeIcons;
            }
            set
            {
                // PHASE 4 : Déléguer vers le manager si disponible
                if (IsManagerArchitectureAvailable && _visibilityManager != null)
                {
                    _visibilityManager.ShowContentTypeIcons = value;
                }
                else
                {
                    // Fallback vers l'implémentation existante
                    SetProperty(ref _showContentTypeIcons, value);
                }
            }
        }

        /// <summary>
        /// Indique si les indicateurs d'épinglage doivent être affichés.
        /// PHASE 4 : Délégation vers VisibilityViewModelManager
        /// </summary>
        public bool ShowPinIndicators
        {
            get
            {
                // PHASE 4 : Déléguer vers le manager si disponible
                if (IsManagerArchitectureAvailable && _visibilityManager != null)
                {
                    return _visibilityManager.ShowPinIndicators;
                }

                // Fallback vers l'implémentation existante
                return _showPinIndicators;
            }
            set
            {
                // PHASE 4 : Déléguer vers le manager si disponible
                if (IsManagerArchitectureAvailable && _visibilityManager != null)
                {
                    _visibilityManager.ShowPinIndicators = value;
                }
                else
                {
                    // Fallback vers l'implémentation existante
                    SetProperty(ref _showPinIndicators, value);
                }
            }
        }

        /// <summary>
        /// Indique si les prévisualisations d'images doivent être affichées.
        /// PHASE 4 : Délégation vers VisibilityViewModelManager
        /// </summary>
        public bool ShowImagePreviews
        {
            get
            {
                // PHASE 4 : Déléguer vers le manager si disponible
                if (IsManagerArchitectureAvailable && _visibilityManager != null)
                {
                    return _visibilityManager.ShowImagePreviews;
                }

                // Fallback vers l'implémentation existante
                return _showImagePreviews;
            }
            set
            {
                // PHASE 4 : Déléguer vers le manager si disponible
                if (IsManagerArchitectureAvailable && _visibilityManager != null)
                {
                    _visibilityManager.ShowImagePreviews = value;
                }
                else
                {
                    // Fallback vers l'implémentation existante
                    SetProperty(ref _showImagePreviews, value);
                }
            }
        }

        /// <summary>
        /// Indique si une opération de drag & drop est en cours.
        /// PHASE 4 : Délégation vers DragDropViewModelManager
        /// </summary>
        public bool IsDragDropActive
        {
            get
            {
                // PHASE 4 : Déléguer vers le manager si disponible
                if (IsManagerArchitectureAvailable && _dragDropManager != null)
                {
                    return _dragDropManager.IsDragDropActive;
                }

                // Fallback vers l'implémentation existante
                return _isDragDropActive;
            }
        }

        /// <summary>
        /// Type de données actuellement en cours de drag & drop.
        /// PHASE 4 : Délégation vers DragDropViewModelManager
        /// </summary>
        public ClipboardDataType? CurrentDragDataType
        {
            get
            {
                // PHASE 4 : Déléguer vers le manager si disponible
                if (IsManagerArchitectureAvailable && _dragDropManager != null)
                {
                    return _dragDropManager.CurrentDragDataType;
                }

                // Fallback vers l'implémentation existante
                return _currentDragDataType;
            }
        }

        /// <summary>
        /// Indique si le drop est autorisé à la position actuelle.
        /// PHASE 4 : Délégation vers DragDropViewModelManager
        /// </summary>
        public bool IsDropAllowed
        {
            get
            {
                // PHASE 4 : Déléguer vers le manager si disponible
                if (IsManagerArchitectureAvailable && _dragDropManager != null)
                {
                    return _dragDropManager.IsDropAllowed;
                }

                // Fallback vers l'implémentation existante
                return _isDropAllowed;
            }
        }

        /// <summary>
        /// Constructeur principal avec injection de dépendances.
        /// Utilisez ClipboardHistoryViewModelFactory.CreateWithDependencyInjection() pour une création simplifiée.
        /// </summary>
        /// <param name="clipboardHistoryManager">Gestionnaire d'historique du presse-papiers.</param>
        /// <param name="clipboardInteractionService">Service d'interaction avec le presse-papiers.</param>
        /// <param name="settingsManager">Gestionnaire des paramètres de l'application.</param>
        /// <param name="userNotificationService">Service de notification utilisateur.</param>
        /// <param name="userInteractionService">Service d'interaction utilisateur.</param>
        /// <summary>
        /// Constructeur principal avec injection de dépendances.
        /// Utilise une approche modulaire : validation et assignation des champs dans le constructeur,
        /// initialisation complexe déléguée aux services via InitializeAsync().
        ///
        /// PHASE 4 - CONSTRUCTEUR SIMPLIFIÉ : Complexité réduite de 12 à ≤ 5 points.
        /// Recommandation : Utilisez ClipboardHistoryViewModelFactory.CreateWithSOLIDArchitecture() pour une architecture plus propre.
        /// </summary>
        /// <param name="clipboardHistoryManager">Gestionnaire de l'historique du presse-papiers</param>
        /// <param name="clipboardInteractionService">Service d'interaction avec le presse-papiers</param>
        /// <param name="settingsManager">Gestionnaire des paramètres</param>
        /// <param name="userNotificationService">Service de notification utilisateur</param>
        /// <param name="userInteractionService">Service d'interaction utilisateur</param>
        /// <param name="serviceProvider">Fournisseur de services pour l'injection de dépendances</param>
        /// <param name="renameService">Service de renommage</param>
        /// <param name="deletionResultLogger">Logger pour les résultats de suppression (optionnel)</param>
        /// <param name="collectionHealthService">Service de santé des collections (optionnel)</param>
        /// <param name="visibilityStateManager">Gestionnaire d'état de visibilité (optionnel)</param>
        /// <param name="newItemCreationOrchestrator">Orchestrateur de création d'éléments (optionnel)</param>
        /// <param name="testEnvironmentDetector">Détecteur d'environnement de test (optionnel)</param>
        /// <summary>
        /// Constructeur Pure SOLID avec DTO de dépendances (PHASE 5 - PERFECTIONNEMENT FINAL).
        /// Réduit le nombre de paramètres de 13 à 2 pour améliorer la cohésion et la lisibilité.
        /// Contient maintenant toute la logique d'initialisation (plus de délégation).
        /// </summary>
        /// <param name="dependencies">DTO contenant toutes les dépendances obligatoires</param>
        /// <param name="optionalServices">DTO contenant les services optionnels</param>
        internal ClipboardHistoryViewModel(ViewModelDependencies dependencies,
                                         OptionalServicesDependencies? optionalServices = null)
        {
            // === VALIDATION DES PARAMÈTRES ===
            if (dependencies == null)
                throw new ArgumentNullException(nameof(dependencies));

            // === ASSIGNATION DES CHAMPS OBLIGATOIRES ===
            _clipboardHistoryManager = dependencies.ClipboardHistoryManager;
            _clipboardInteractionService = dependencies.ClipboardInteractionService;
            _settingsManager = dependencies.SettingsManager;
            _userNotificationService = dependencies.UserNotificationService;
            _userInteractionService = dependencies.UserInteractionService;
            _serviceProvider = dependencies.ServiceProvider;
            _renameService = dependencies.RenameService;

            // === ASSIGNATION DES MODULES ===
            _historyModule = dependencies.HistoryModule;
            _commandModule = dependencies.CommandModule;
            _creationModule = dependencies.CreationModule;

            // === RÉSOLUTION DES SERVICES OPTIONNELS ===
            _deletionResultLogger = optionalServices?.DeletionResultLogger ?? _serviceProvider.GetService<IDeletionResultLogger>();
            _collectionHealthService = optionalServices?.CollectionHealthService ?? _serviceProvider.GetService<ICollectionHealthService>();
            _visibilityStateManager = optionalServices?.VisibilityStateManager ?? _serviceProvider.GetService<IVisibilityStateManager>();
            _newItemCreationOrchestrator = optionalServices?.NewItemCreationOrchestrator ?? _serviceProvider.GetService<INewItemCreationOrchestrator>();
            _testEnvironmentDetector = optionalServices?.TestEnvironmentDetector ?? _serviceProvider.GetService<ITestEnvironmentDetector>();
            _settingsWindowService = optionalServices?.SettingsWindowService ?? _serviceProvider.GetService<ClipboardPlus.Core.Services.Windows.ISettingsWindowService>();

            // === SERVICES POUR REFACTORISATION SUPPRIMERELEMENT V2 ===
            _deletionService = _serviceProvider.GetService<IDeletionService>();
            _deletionUIValidator = _serviceProvider.GetService<IDeletionUIValidator>();
            _deletionUIHandler = _serviceProvider.GetService<IDeletionUIHandler>();
            _deletionUINotificationService = _serviceProvider.GetService<IDeletionUINotificationService>();

            // === SERVICES COMPLEXES (via ServiceResolver unifié) ===
            var serviceResolver = new ServiceResolver();
            var complexServices = serviceResolver.ResolveComplexServices(_serviceProvider);
            _loggingService = complexServices.LoggingService;
            _deletionDiagnostic = complexServices.DeletionDiagnostic;

            // === CRÉATION DU SYNCHRONIZER (sans orchestrateur) ===
            _historyCollectionSynchronizer = new HistoryCollectionSynchronizer(
                this,
                HistoryItems,
                _clipboardHistoryManager,
                _loggingService,
                _deletionDiagnostic,
                null // L'orchestrateur sera ajouté dans InitializeAsync()
            );

            // === PHASE 5 : PAS D'INITIALISATION DES COMMANDES SI SKIP ===
            // Les commandes sont initialisées par le Builder via CommandInitializer.InitializeAllCommands()
            // Cela élimine la double initialisation et respecte le principe de responsabilité unique
            bool skipCommandInitialization = optionalServices?.SkipCommandInitialization ?? false;
            if (!skipCommandInitialization)
            {
                // Initialisation des commandes pour les tests directs
                var commandInitializer = new ClipboardPlus.UI.ViewModels.Construction.Implementations.CommandInitializer();
                commandInitializer.InitializeAllCommands(this);
            }

            // === LOG D'INITIALISATION ===
            _loggingService?.LogInfo("ClipboardHistoryViewModel - Constructeur DTO SOLID terminé (PHASE 5)");
        }
        /// <summary>
        /// PHASE 4 : Constructeur avec architecture managériale moderne.
        /// Remplace l'architecture fragmentée des fichiers partiels par 6 managers spécialisés.
        /// Ce constructeur représente l'évolution vers une architecture plus maintenable et testable.
        /// </summary>
        /// <param name="managers">DTO contenant les 6 managers spécialisés</param>
        /// <param name="dependencies">DTO contenant les dépendances de base (optionnel pour compatibilité)</param>
        internal ClipboardHistoryViewModel(ManagerDependencies managers, ViewModelDependencies? dependencies = null)
        {
            // === VALIDATION DES PARAMÈTRES ===
            if (managers == null)
                throw new ArgumentNullException(nameof(managers));

            // === ASSIGNATION DES MANAGERS ===
            _historyManager = managers.HistoryManager ?? throw new ArgumentNullException(nameof(managers.HistoryManager));
            _commandManager = managers.CommandManager ?? throw new ArgumentNullException(nameof(managers.CommandManager));
            _itemCreationManager = managers.ItemCreationManager ?? throw new ArgumentNullException(nameof(managers.ItemCreationManager));
            _eventManager = managers.EventManager ?? throw new ArgumentNullException(nameof(managers.EventManager));
            _visibilityManager = managers.VisibilityManager ?? throw new ArgumentNullException(nameof(managers.VisibilityManager));
            _dragDropManager = managers.DragDropManager ?? throw new ArgumentNullException(nameof(managers.DragDropManager));

            // === ASSIGNATION DES DÉPENDANCES DE BASE (si fournies) ===
            if (dependencies != null)
            {
                _clipboardHistoryManager = dependencies.ClipboardHistoryManager;
                _clipboardInteractionService = dependencies.ClipboardInteractionService;
                _settingsManager = dependencies.SettingsManager;
                _userNotificationService = dependencies.UserNotificationService;
                _userInteractionService = dependencies.UserInteractionService;
                _renameService = dependencies.RenameService;
                _serviceProvider = dependencies.ServiceProvider;

                // Modules (pour compatibilité)
                _historyModule = dependencies.HistoryModule;
                _commandModule = dependencies.CommandModule;
                _creationModule = dependencies.CreationModule;
            }

            // === INITIALISATION DES COLLECTIONS ===
            // La collection legacy est déjà initialisée comme champ
            // HistoryItems utilise maintenant la délégation vers les managers

            // === DÉLÉGATION VERS LES MANAGERS ===
            // Les propriétés et commandes seront déléguées vers les managers appropriés
            // Cette logique sera implémentée dans les étapes suivantes

            // === LOG D'INITIALISATION ===
            _loggingService?.LogInfo("ClipboardHistoryViewModel - Constructeur avec architecture managériale terminé (PHASE 4)");
        }





        /// <summary>
        /// Initialise de manière asynchrone tous les composants complexes du ViewModel.
        /// Cette méthode contient toute la logique d'initialisation extraite du constructeur monolithique.
        /// Elle doit être appelée par le Builder après la construction de l'instance.
        /// </summary>
        /// <returns>Task représentant l'opération d'initialisation asynchrone</returns>
        public async Task InitializeAsync()
        {
            _loggingService?.LogInfo("ClipboardHistoryViewModel - Début de l'initialisation asynchrone");

            // === CONFIGURATION DES ÉVÉNEMENTS ===
            _loggingService?.LogInfo($"[DIAGNOSTIC_MESSAGES] Abonnement aux messages WeakReferenceMessenger - ViewModel: {GetHashCode()}");
            WeakReferenceMessenger.Default.Register<HideTimestampChangedMessage>(this, OnHideTimestampChanged);
            _loggingService?.LogInfo($"[DIAGNOSTIC_MESSAGES] Abonné à HideTimestampChangedMessage - ViewModel: {GetHashCode()}");
            WeakReferenceMessenger.Default.Register<HideItemTitleChangedMessage>(this, OnHideItemTitleChanged);
            _loggingService?.LogInfo($"[DIAGNOSTIC_MESSAGES] Abonné à HideItemTitleChangedMessage - ViewModel: {GetHashCode()}");

            // Écouter les événements du nouveau gestionnaire de visibilité
            if (_visibilityStateManager != null)
            {
                _visibilityStateManager.VisibilityChanged += OnVisibilityStateChanged;
                _loggingService?.LogInfo("Gestionnaire de visibilité configuré");
            }

            // === INITIALISATION DE L'ORCHESTRATEUR DANS LE SYNCHRONIZER ===
            if (_historyChangeOrchestrator != null)
            {
                _historyCollectionSynchronizer.InitializeOrchestrator(_historyChangeOrchestrator);
                _loggingService?.LogInfo("Orchestrateur initialisé dans le HistoryCollectionSynchronizer");
            }

            // === COMMANDES DÉJÀ INITIALISÉES DANS LE CONSTRUCTEUR ===
            // Les commandes sont initialisées dans le constructeur

            // === INITIALISATION DES SERVICES OPTIONNELS ===
            InitializeMigrationServices();

            // === INITIALISATION DES MANAGERS (PHASE 4) ===
            // CORRECTION CRITIQUE : Initialiser le HistoryViewModelManager
            if (IsManagerArchitectureAvailable && _historyManager != null)
            {
                _loggingService?.LogInfo("🔧 [DIAGNOSTIC] Initialisation du HistoryViewModelManager...");
                await _historyManager.InitializeAsync();
                _loggingService?.LogInfo("✅ [DIAGNOSTIC] HistoryViewModelManager initialisé avec succès");
            }

            // === INITIALISATION DE L'ORCHESTRATEUR ===
            if (_historyChangeOrchestrator != null && _featureFlagService != null)
            {
                InitializeHistoryChangeOrchestrator(_historyChangeOrchestrator, _featureFlagService);
            }

            // === ABONNEMENT AUX ÉVÉNEMENTS DU HISTORY MODULE ===
            // CORRECTION CRITIQUE: S'abonner aux événements du HistoryModule pour synchroniser l'UI
            if (_historyModule != null)
            {
                _historyModule.HistoryChanged += OnHistoryModuleChanged;
                _loggingService?.LogInfo("Abonnement aux événements HistoryModule configuré");
            }

            // === INITIALISATION DU SERVICE DE SANTÉ ===
            InitializeCollectionHealthService();

            // === CHARGEMENT INITIAL DE L'HISTORIQUE ===
            // Le chargement nécessite un HistoryChangeOrchestrator. Si non disponible, on l'ignore pour les tests.
            try
            {
                await LoadHistoryAsync();
                _loggingService?.LogInfo("ClipboardHistoryViewModel - Chargement de l'historique terminé");
            }
            catch (InvalidOperationException ex) when (ex.Message.Contains("HistoryChangeOrchestrator"))
            {
                _loggingService?.LogInfo("ClipboardHistoryViewModel - Chargement de l'historique ignoré (HistoryChangeOrchestrator non disponible)");
            }

            _loggingService?.LogInfo("ClipboardHistoryViewModel - Initialisation asynchrone terminée");

            // Marquer l'initialisation comme terminée pour les tests de régression
            IsInitialized = true;
        }

        /// <summary>
        /// PHASE 4 : Applique un filtre de recherche via l'architecture modulaire pure
        /// Try/catch supprimé - architecture modulaire validée et stable
        /// PHASE 4 : Délégation vers HistoryViewModelManager si disponible.
        /// </summary>
        /// <param name="searchFilter">Filtre de recherche à appliquer</param>
        private void ApplySearchFilterUsingModule(string? searchFilter)
        {
            var operationId = Guid.NewGuid().ToString("N")[..8];
            _loggingService?.LogInfo($"[MODULAIRE] ApplySearchFilter [{operationId}] - Filtre: '{searchFilter ?? "null"}'");

            // PHASE 4 : Déléguer vers le manager si disponible
            if (IsManagerArchitectureAvailable && _historyManager != null)
            {
                _loggingService?.LogInfo($"[PHASE4] Délégation vers HistoryViewModelManager [{operationId}]");
                _historyManager.ApplySearchFilter(searchFilter);
                _loggingService?.LogInfo($"[PHASE4] ApplySearchFilter [{operationId}] réussi via manager - Éléments: {_historyManager.FilteredItemCount}");
                return;
            }

            // Fallback vers l'implémentation existante
            // PHASE 4 : Validation de sécurité pour les tests
            if (_historyModule == null)
            {
                throw new InvalidOperationException("HistoryModule non disponible - architecture modulaire requise");
            }

            // PHASE 4 : Architecture modulaire pure - try/catch supprimé après validation
            _historyModule.ApplyFilter(searchFilter);

            // Synchroniser HistoryItems avec FilteredItems du module
            _legacyHistoryItems.Clear();
            foreach (var item in _historyModule.FilteredItems)
            {
                _legacyHistoryItems.Add(item);
            }

            _loggingService?.LogInfo($"[MODULAIRE] ApplySearchFilter [{operationId}] réussi - Éléments: {_historyModule.FilteredItemCount}, UI synchronisée: {_legacyHistoryItems.Count}");
        }

        /// <summary>
        /// Charge l'historique via l'architecture modulaire et synchronise les données avec l'UI.
        /// PHASE 4 : Délégation vers HistoryViewModelManager si disponible.
        /// </summary>
        public async Task LoadHistoryAsync(string? callContext = null)
        {
            string context = string.IsNullOrWhiteSpace(callContext) ? "vm_direct_call" : callContext;
            _loggingService?.LogInfo($"[VM] LoadHistoryAsync appelé avec le contexte: {context}");

            // PHASE 4 : Déléguer vers le manager si disponible
            if (IsManagerArchitectureAvailable && _historyManager != null)
            {
                _loggingService?.LogInfo("[PHASE4] Délégation vers HistoryViewModelManager");
                await _historyManager.LoadHistoryAsync(context);
                _loggingService?.LogInfo($"[PHASE4] Chargement réussi via manager. {_historyManager.TotalItemCount} éléments chargés");
                return;
            }

            // Fallback vers l'implémentation existante
            // Validation de sécurité
            if (_historyModule == null)
            {
                throw new InvalidOperationException("HistoryModule non disponible - architecture modulaire requise");
            }

            _loggingService?.LogInfo("[MODULAIRE] Chargement via HistoryModule");
            await _historyModule.LoadHistoryAsync(context);
            _loggingService?.LogInfo($"[MODULAIRE] Chargement réussi. {_historyModule.TotalItemCount} éléments chargés");

            // Synchroniser les données du module avec l'UI
            SynchronizeHistoryModuleWithUI("module_load_sync");

            RecordModuleSuccess("LoadHistoryAsync");

            // MAINTENANT que HistoryItems est peuplé, appliquer les paramètres
            _loggingService?.LogInfo($"[VM] Application des états de visibilité initiaux pour {HistoryItems.Count} éléments.");

            // Lire les paramètres pour les logs (mais ne plus les utiliser directement)
            HideTimestamp = _settingsManager.HideTimestamp;
            HideItemTitle = _settingsManager.HideItemTitle;
            _loggingService?.LogInfo($"[VM] Visibilité timestamp : {!HideTimestamp}");
            _loggingService?.LogInfo($"[VM] Visibilité titres : {!HideItemTitle}");

            // Une seule méthode centralisée pour toute la visibilité
            ApplyCompleteVisibilityViaSolid();
        }

        private void OnHideTimestampChanged(object recipient, HideTimestampChangedMessage message)
        {
            _loggingService?.LogInfo($"[TIMESTAMP_CHANGE] Message HideTimestampChanged reçu : {message.HideTimestamp}");

            // Vérifier si la valeur change réellement
            bool oldValue = HideTimestamp;
            bool newValue = message.HideTimestamp;
            _loggingService?.LogInfo($"[TIMESTAMP_CHANGE] État précédent: {oldValue}, Nouvel état: {newValue}, Changement: {oldValue != newValue}");

            // Mettre à jour le flag dans le ViewModel
            HideTimestamp = newValue;
            _loggingService?.LogInfo($"[TIMESTAMP_CHANGE] Flag HideTimestamp mis à jour dans le ViewModel: {HideTimestamp}");

            _loggingService?.LogInfo($"[TIMESTAMP_CHANGE] Message reçu - HideTimestamp: {message.HideTimestamp}");

            // CORRECTION CRITIQUE : Synchroniser le VisibilityStateManager avec les nouveaux paramètres
            _loggingService?.LogInfo($"[TIMESTAMP_CHANGE] Synchronisation VisibilityStateManager");
            _visibilityStateManager?.UpdateTimestampVisibilityFromSettings(message.HideTimestamp);

            // Utiliser la méthode centralisée au lieu de la logique dispersée
            _loggingService?.LogInfo($"[TIMESTAMP_CHANGE] Application via méthode centralisée");
            ApplyCompleteVisibilityViaSolid();

            _loggingService?.LogInfo($"[TIMESTAMP_CHANGE] Mise à jour terminée");
        }

        private void OnHideItemTitleChanged(object recipient, HideItemTitleChangedMessage message)
        {
            _loggingService?.LogInfo($"[ITEM_TITLE_CHANGE] DIAGNOSTIC CRITIQUE - Message HideItemTitleChanged reçu : {message.HideItemTitle}");

            // Vérifier si la valeur change réellement
            bool oldValue = HideItemTitle;
            bool newValue = message.HideItemTitle;
            _loggingService?.LogInfo($"[ITEM_TITLE_CHANGE] DIAGNOSTIC CRITIQUE - État précédent: {oldValue}, Nouvel état: {newValue}, Changement: {oldValue != newValue}");

            _loggingService?.LogInfo($"[ITEM_TITLE_CHANGE] DIAGNOSTIC CRITIQUE - ViewModel: {GetHashCode()}, Thread courant: {System.Threading.Thread.CurrentThread.ManagedThreadId}");

            // Mettre à jour le flag dans le ViewModel
            HideItemTitle = newValue;
            _loggingService?.LogInfo($"[ITEM_TITLE_CHANGE] DIAGNOSTIC CRITIQUE - Flag HideItemTitle mis à jour dans le ViewModel: {HideItemTitle}");

            // Vérifier le nombre d'éléments à traiter
            _loggingService?.LogInfo($"[ITEM_TITLE_CHANGE] DIAGNOSTIC CRITIQUE - Préparation à l'application sur {HistoryItems.Count} élément(s)");

            // CORRECTION CRITIQUE : Synchroniser le VisibilityStateManager avec les nouveaux paramètres
            _loggingService?.LogInfo($"[ITEM_TITLE_CHANGE] DIAGNOSTIC CRITIQUE - Synchronisation VisibilityStateManager");
            _visibilityStateManager?.UpdateTitleVisibilityFromSettings(message.HideItemTitle);

            // Utiliser la méthode centralisée au lieu de ApplyItemTitleVisibility()
            _loggingService?.LogInfo($"[ITEM_TITLE_CHANGE] DIAGNOSTIC CRITIQUE - Application via méthode centralisée");
            ApplyCompleteVisibilityViaSolid();
            _loggingService?.LogInfo($"[ITEM_TITLE_CHANGE] DIAGNOSTIC CRITIQUE - Mise à jour terminée");
        }

        private void ApplyItemTitleVisibility()
        {
            var timestamp = DateTime.Now.ToString("HH:mm:ss.fff");
            _loggingService?.LogInfo($"[TITLE_VISIBILITY_{timestamp}] Début ApplyItemTitleVisibility");

            // Calculer la visibilité via IVisibilityStateManager
            if (_visibilityStateManager == null)
            {
                _loggingService?.LogError($"[TITLE_VISIBILITY_{timestamp}] IVisibilityStateManager non configuré");
                throw new InvalidOperationException("IVisibilityStateManager n'est pas configuré.");
            }

            // Protection contre les modifications concurrentes de la collection
            List<ClipboardItem> itemsToProcess;
            try
            {
                lock (HistoryItems)
                {
                    itemsToProcess = HistoryItems.Where(item => item != null).ToList();
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"[TITLE_VISIBILITY_{timestamp}] Erreur lors de la copie de HistoryItems: {ex.Message}");
                return;
            }

            foreach (var item in itemsToProcess)
            {
                // Utiliser le gestionnaire centralisé pour calculer la visibilité
                bool shouldShowTitle = _visibilityStateManager.ShouldShowTitle(item);
                bool shouldShowTimestamp = _visibilityStateManager.ShouldShowTimestamp(item);

                // Mettre à jour les propriétés du modèle avec les valeurs calculées
                item.IsTitleVisible = shouldShowTitle;
                item.IsTimestampVisible = shouldShowTimestamp;
            }

            _loggingService?.LogInfo($"[TITLE_VISIBILITY_{timestamp}] Fin ApplyItemTitleVisibility - {itemsToProcess.Count} éléments mis à jour");
        }

        /// <summary>
        /// Applique la visibilité complète (titres ET horodatages) via le gestionnaire centralisé
        /// MÉTHODE CENTRALISÉE - Remplace toute la logique dispersée
        /// CORRECTION CRITIQUE: Rendue publique pour permettre la réapplication après synchronisation
        /// </summary>
        public void ApplyCompleteVisibilityViaSolid()
        {
            var timestamp = DateTime.Now.ToString("HH:mm:ss.fff");
            _loggingService?.LogInfo($"[COMPLETE_VISIBILITY_{timestamp}] Début ApplyCompleteVisibilityViaSolid");

            // Vérification obligatoire
            if (_visibilityStateManager == null)
            {
                _loggingService?.LogError($"[COMPLETE_VISIBILITY_{timestamp}] IVisibilityStateManager non configuré");
                throw new InvalidOperationException("IVisibilityStateManager n'est pas configuré.");
            }

            int titlesUpdated = 0;
            int timestampsUpdated = 0;

            // Créer une copie de la liste pour éviter les problèmes de concurrence
            List<ClipboardItem> itemsToProcess;
            try
            {
                // Protection contre les modifications concurrentes de la collection
                lock (HistoryItems)
                {
                    itemsToProcess = HistoryItems.Where(item => item != null).ToList();
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"[COMPLETE_VISIBILITY_{timestamp}] Erreur lors de la copie de HistoryItems: {ex.Message}");
                return; // Sortir silencieusement pour éviter de casser l'application
            }

            foreach (var item in itemsToProcess)
            {
                if (item == null) continue;

                bool shouldShowTitle = _visibilityStateManager.ShouldShowTitle(item);
                bool shouldShowTimestamp = _visibilityStateManager.ShouldShowTimestamp(item);

                // --- NOUVELLE LOGIQUE DE MISE À JOUR ---
                bool titleChanged = item.IsTitleVisible != shouldShowTitle;
                bool timestampChanged = item.IsTimestampVisible != shouldShowTimestamp;

                if (titleChanged)
                {
                    item.IsTitleVisible = shouldShowTitle;
                    titlesUpdated++;
                }
                if (timestampChanged)
                {
                    item.IsTimestampVisible = shouldShowTimestamp;
                    timestampsUpdated++;
                }
                // --- FIN NOUVELLE LOGIQUE ---
            }

            _loggingService?.LogInfo($"[COMPLETE_VISIBILITY_{timestamp}] Fin - {HistoryItems.Count} éléments traités, {titlesUpdated} titres modifiés, {timestampsUpdated} horodatages modifiés");

            // === CORRECTION 3 : FORÇAGE SYSTÉMATIQUE ===
            if (titlesUpdated > 0 || timestampsUpdated > 0)
            {
                _loggingService?.LogInfo($"[COMPLETE_VISIBILITY_{timestamp}] FORÇAGE mise à jour UI");

                foreach (var item in itemsToProcess.Where(i => i != null))
                {
                    // Déclencher PropertyChanged sur les propriétés de visibilité pour TOUS les items
                    // Cela résout les problèmes de désynchronisation où la valeur locale de l'UI est différente
                    // de la valeur du modèle, même si la valeur du modèle elle-même ne change pas.
                    item.ForcePropertyChanged(nameof(item.IsTitleVisible));
                    item.ForcePropertyChanged(nameof(item.IsTimestampVisible));
                }

                _loggingService?.LogInfo($"[COMPLETE_VISIBILITY_{timestamp}] FORÇAGE terminé - UI devrait être mise à jour");
            }
        }

        /// <summary>
        /// Vérifie si l'application est dans un contexte UI valide pour afficher des fenêtres
        /// </summary>
        private bool IsInValidUiContext()
        {
            try
            {
                // Vérifier si nous sommes dans un thread UI
                if (WpfApplication.Current?.Dispatcher == null || !WpfApplication.Current.Dispatcher.CheckAccess())
                {
                    return false;
                }

                // Vérifier si la fenêtre principale existe et est chargée
                if (WpfApplication.Current.MainWindow == null || !WpfApplication.Current.MainWindow.IsLoaded)
                {
                    return false;
                }

                // Vérifier si nous sommes dans un contexte de test
                // REFACTORISÉ : Utilise maintenant IsInTestEnvironment() qui utilise ITestEnvironmentDetector
                if (IsInTestEnvironment())
                {
                    return false; // En mode test, pas de contexte UI valide
                }

                return true;
            }
            catch
            {
                return false;
            }
        }

        // Méthode pour se désabonner lors du nettoyage (par exemple, si le ViewModel est "fermé" ou "disposé")
        public void Cleanup()
        {
            _historyCollectionSynchronizer?.Unsubscribe();

            // CORRECTION CRITIQUE: Se désabonner des événements du HistoryModule
            if (_historyModule != null)
            {
                _historyModule.HistoryChanged -= OnHistoryModuleChanged;
            }

            _loggingService?.LogInfo("ClipboardHistoryViewModel Cleanup: Désabonnement du HistoryCollectionSynchronizer et HistoryModule.");
        }

        /// <summary>
        /// Force la synchronisation des collections même si une opération est en cours.
        /// Utilisé après des opérations de nettoyage en lot.
        /// </summary>
        public async Task ForceSynchronizationAsync(string reason = "Manual force sync")
        {
            _loggingService?.LogInfo($"[VM] ForceSynchronizationAsync appelé: {reason}");
            await _historyCollectionSynchronizer.ForceSynchronizationAsync(reason);
        }

        /// <summary>
        /// CORRECTION CRITIQUE: Synchronise les données du HistoryModule avec l'UI du ViewModel.
        /// Cette méthode corrige le bug où les éléments chargés par le module n'apparaissent pas dans l'UI.
        /// CORRECTION THREADING: S'assure que la synchronisation se fait sur le thread UI.
        /// </summary>
        private void SynchronizeHistoryModuleWithUI(string reason = "module_sync")
        {
            try
            {
                _loggingService?.LogInfo($"[MODULE_SYNC] Début synchronisation: {reason}");

                // Récupérer les éléments du module
                var moduleItems = _historyModule?.HistoryItems?.ToList() ?? new List<ClipboardItem>();
                _loggingService?.LogInfo($"[MODULE_SYNC] Module contient {moduleItems.Count} éléments");

                // ✅ CORRECTION CRITIQUE: S'assurer que la synchronisation se fait sur le thread UI
                if (System.Windows.Application.Current?.Dispatcher != null)
                {
                    if (System.Windows.Application.Current.Dispatcher.CheckAccess())
                    {
                        // Déjà sur le thread UI, exécuter directement
                        ExecuteModuleSynchronization(reason);
                    }
                    else
                    {
                        // Pas sur le thread UI, dispatcher vers le thread UI
                        System.Windows.Application.Current.Dispatcher.Invoke(() => ExecuteModuleSynchronization(reason));
                    }
                }
                else
                {
                    // Fallback si Dispatcher non disponible (tests unitaires)
                    _loggingService?.LogWarning($"[MODULE_SYNC] Dispatcher non disponible, exécution directe (mode test?)");
                    ExecuteModuleSynchronization(reason);
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"[MODULE_SYNC] Erreur lors de la synchronisation: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Exécute la synchronisation du module sur le thread UI.
        /// Cette méthode doit TOUJOURS être appelée depuis le thread UI.
        /// </summary>
        private void ExecuteModuleSynchronization(string reason)
        {
            try
            {
                // Synchroniser avec la collection UI via le synchronizer
                if (_historyCollectionSynchronizer != null)
                {
                    _historyCollectionSynchronizer.SynchronizeUIDirectly($"module_to_ui_{reason}");
                    _loggingService?.LogInfo($"[MODULE_SYNC] Synchronisation UI terminée via synchronizer");
                }
                else
                {
                    _loggingService?.LogWarning($"[MODULE_SYNC] HistoryCollectionSynchronizer non disponible");
                }

                // Appliquer les paramètres de visibilité
                ApplyCompleteVisibilityViaSolid();

                _loggingService?.LogInfo($"[MODULE_SYNC] Synchronisation terminée: {HistoryItems.Count} éléments dans l'UI");
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"[MODULE_SYNC] Erreur lors de l'exécution de la synchronisation: {ex.Message}", ex);
                throw; // Propager l'erreur pour que l'appelant puisse la gérer
            }
        }

        /// <summary>
        /// CORRECTION CRITIQUE: Gestionnaire d'événements pour les changements du HistoryModule.
        /// Cette méthode synchronise automatiquement l'UI quand le module change.
        /// </summary>
        private void OnHistoryModuleChanged(object? sender, ClipboardPlus.Modules.History.HistoryChangedEventArgs e)
        {
            try
            {
                _loggingService?.LogInfo($"[MODULE_EVENT] HistoryModule changé: {e.ChangeType}, Context: {e.Context}");

                // Synchroniser l'UI avec les nouvelles données du module
                SynchronizeHistoryModuleWithUI($"event_{e.ChangeType}");

                _loggingService?.LogInfo($"[MODULE_EVENT] Synchronisation terminée pour {e.ChangeType}");
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"[MODULE_EVENT] Erreur lors de la gestion de l'événement HistoryModule: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Obtient le gestionnaire d'historique du presse-papiers.
        /// Utilisé par les services pour accéder au manager.
        /// </summary>
        public IClipboardHistoryManager? GetClipboardHistoryManager()
        {
            return _clipboardHistoryManager;
        }

        /// <summary>
        /// Initialise les services optionnels si disponibles dans le conteneur de services.
        /// </summary>
        private void InitializeMigrationServices()
        {
            try
            {
                if (WpfApplication.Current is App appInstance && appInstance.Services != null)
                {
                    _historyChangeOrchestrator = appInstance.Services.GetService<IHistoryChangeOrchestrator>();
                    _featureFlagService = appInstance.Services.GetService<IFeatureFlagService>();

                    if (_historyChangeOrchestrator != null && _featureFlagService != null)
                    {
                        _loggingService?.LogInfo("[ClipboardHistoryViewModel] Services optionnels initialisés");
                    }
                    else
                    {
                        _loggingService?.LogInfo("[ClipboardHistoryViewModel] Services optionnels non disponibles");
                    }
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"[ClipboardHistoryViewModel] Erreur lors de l'initialisation des services optionnels: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Initialise le service de santé des collections
        /// </summary>
        private void InitializeCollectionHealthService()
        {
            try
            {
                if (_collectionHealthService != null)
                {
                    // Enregistrer ce ViewModel pour surveillance
                    if (_collectionHealthService is CollectionHealthService healthService)
                    {
                        healthService.RegisterViewModel(this);
                    }

                    // S'abonner aux événements de santé
                    _collectionHealthService.HealthChanged += OnCollectionHealthChanged;

                    // Démarrer la surveillance automatique
                    _collectionHealthService.StartMonitoring();

                    _loggingService?.LogInfo("🏥 Service de santé des collections initialisé et surveillance démarrée");
                }
                else
                {
                    _loggingService?.LogWarning("🏥 Service de santé des collections non disponible");
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"🏥 Erreur initialisation service de santé: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Gestionnaire d'événement pour les changements de santé des collections
        /// </summary>
        private void OnCollectionHealthChanged(object? sender, CollectionHealthEventArgs e)
        {
            try
            {
                if (!e.IsHealthy)
                {
                    _loggingService?.LogWarning($"🏥 [{e.Report.OperationId}] Santé dégradée détectée - Issues: {string.Join(", ", e.Report.Issues)}");

                    // Tenter une réparation automatique
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            var repaired = await _collectionHealthService!.RepairInconsistenciesAsync();
                            if (repaired)
                            {
                                _loggingService?.LogInfo($"🏥 [{e.Report.OperationId}] Réparation automatique réussie");
                            }
                            else
                            {
                                _loggingService?.LogWarning($"🏥 [{e.Report.OperationId}] Réparation automatique échouée");
                            }
                        }
                        catch (Exception ex)
                        {
                            _loggingService?.LogError($"🏥 [{e.Report.OperationId}] Erreur réparation automatique: {ex.Message}", ex);
                        }
                    });
                }
                else if (e.WasHealthy != e.IsHealthy)
                {
                    _loggingService?.LogInfo($"🏥 [{e.Report.OperationId}] Santé restaurée");
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"🏥 Erreur gestionnaire santé: {ex.Message}", ex);
            }
        }

        // === MÉTHODES HELPER ===

        /// <summary>
        /// Gestionnaire d'événements pour les changements de visibilité (nouvelle architecture)
        /// </summary>
        private void OnVisibilityStateChanged(object? sender, ClipboardPlus.Core.Services.Visibility.VisibilityChangedEventArgs e)
        {
            _loggingService?.LogInfo($"[VISIBILITY] Changement de visibilité: {e.Type} = {e.IsVisible}");

            // Notifier l'UI que les propriétés de visibilité ont changé
            switch (e.Type)
            {
                case VisibilityType.Title:
                    OnPropertyChanged(nameof(ShowTitles));
                    break;
                case VisibilityType.Timestamp:
                    OnPropertyChanged(nameof(ShowTimestamps));
                    break;
            }
        }

        /// <summary>
        /// Propriété de visibilité globale des titres
        /// PHASE 4 : Délégation vers VisibilityViewModelManager
        /// </summary>
        public bool ShowTitles
        {
            get
            {
                // PHASE 4 : Déléguer vers le manager si disponible
                if (IsManagerArchitectureAvailable && _visibilityManager != null)
                {
                    return _visibilityManager.ShowTitles;
                }

                // Fallback vers l'implémentation existante
                if (_visibilityStateManager == null)
                {
                    throw new InvalidOperationException("IVisibilityStateManager n'est pas configuré.");
                }
                return _visibilityStateManager.GlobalTitleVisibility;
            }
        }

        /// <summary>
        /// Propriété de visibilité globale des horodatages
        /// PHASE 4 : Délégation vers VisibilityViewModelManager
        /// </summary>
        public bool ShowTimestamps
        {
            get
            {
                // PHASE 4 : Déléguer vers le manager si disponible
                if (IsManagerArchitectureAvailable && _visibilityManager != null)
                {
                    return _visibilityManager.ShowTimestamps;
                }

                // Fallback vers l'implémentation existante
                if (_visibilityStateManager == null)
                {
                    throw new InvalidOperationException("IVisibilityStateManager n'est pas configuré.");
                }
                return _visibilityStateManager.GlobalTimestampVisibility;
            }
        }

        /// <summary>
        /// Détermine si le titre d'un élément spécifique doit être affiché
        /// </summary>
        public bool ShouldShowItemTitle(ClipboardItem item)
        {
            if (_visibilityStateManager == null)
            {
                throw new InvalidOperationException("IVisibilityStateManager n'est pas configuré.");
            }

            return _visibilityStateManager.ShouldShowTitle(item);
        }

        /// <summary>
        /// Détermine si l'horodatage d'un élément spécifique doit être affiché
        /// </summary>
        public bool ShouldShowItemTimestamp(ClipboardItem item)
        {
            if (_visibilityStateManager == null)
            {
                throw new InvalidOperationException("IVisibilityStateManager n'est pas configuré.");
            }

            return _visibilityStateManager.ShouldShowTimestamp(item);
        }

        public void Dispose()
        {
            // Nettoyer le service de santé
            if (_collectionHealthService != null)
            {
                _collectionHealthService.HealthChanged -= OnCollectionHealthChanged;
                _collectionHealthService.StopMonitoring();
            }

            // Nettoyer le gestionnaire de visibilité (nouvelle architecture)
            if (_visibilityStateManager != null)
            {
                _visibilityStateManager.VisibilityChanged -= OnVisibilityStateChanged;
            }

            WeakReferenceMessenger.Default.UnregisterAll(this);
            GC.SuppressFinalize(this);
        }

        #region Public Methods for Command Initialization

        /// <summary>
        /// Méthode publique pour permettre au CommandInitializer d'initialiser les commandes de renommage.
        /// PHASE 5 : Supprimée car les commandes sont maintenant déléguées vers ItemCreationManager.
        /// </summary>
        public void InitializeRenamingCommandsPublic()
        {
            // PHASE 5 : Plus nécessaire - les commandes sont déléguées vers ItemCreationManager
            // InitializeRenamingCommands(); // Supprimé avec ClipboardHistoryViewModel.Renaming.cs
        }

        /// <summary>
        /// Méthode publique pour permettre au CommandInitializer d'initialiser les commandes de nouvel élément.
        /// PHASE 5 : Supprimée car les commandes sont maintenant déléguées vers ItemCreationManager.
        /// </summary>
        public void InitializeNewItemCommandsPublic()
        {
            // PHASE 5 : Plus nécessaire - les commandes sont déléguées vers ItemCreationManager
            // InitializeNewItemCommands(); // Supprimé avec ClipboardHistoryViewModel.NewItem.cs
        }

        /// <summary>
        /// Méthode publique pour rafraîchir les commandes de création d'éléments.
        /// PHASE 5 : Supprimée car les commandes sont maintenant déléguées vers ItemCreationManager.
        /// </summary>
        public void RefreshItemCreationCommands()
        {
            // PHASE 5 : Plus nécessaire - les commandes sont déléguées vers ItemCreationManager
            // La logique de rafraîchissement est maintenant gérée automatiquement par ItemCreationManager
        }

        #region IDropTarget Implementation - PHASE 5 : Délégation vers DragDropViewModelManager

        /// <summary>
        /// Gère l'événement DragOver pour le glisser-déposer.
        /// PHASE 5 : Délégation vers DragDropViewModelManager
        /// </summary>
        /// <param name="dropInfo">Informations sur l'opération de glisser-déposer.</param>
        public void DragOver(WpfDragDrop.IDropInfo dropInfo)
        {
            // PHASE 5 : Délégation vers DragDropViewModelManager avec adaptateur
            _dragDropManager?.DragOver(new DropInfoAdapter(dropInfo));
        }

        /// <summary>
        /// Gère l'événement Drop pour le glisser-déposer.
        /// PHASE 5 : Délégation vers DragDropViewModelManager
        /// </summary>
        /// <param name="dropInfo">Informations sur l'opération de glisser-déposer.</param>
        public void Drop(WpfDragDrop.IDropInfo dropInfo)
        {
            // PHASE 5 : Délégation vers DragDropViewModelManager avec adaptateur
            _dragDropManager?.Drop(new DropInfoAdapter(dropInfo));
        }

        #endregion

        /// <summary>
        /// Méthode publique pour permettre au CommandInitializer d'initialiser les commandes restantes.
        /// PHASE 5 : Supprimée car les commandes sont maintenant déléguées vers CommandViewModelManager.
        /// </summary>
        public void InitializeRemainingCommandsPublic()
        {
            // PHASE 5 : Plus nécessaire - les commandes sont déléguées vers CommandViewModelManager
            // InitializeRemainingCommands(); // Supprimé avec ClipboardHistoryViewModel.Commands.cs
        }

        /// <summary>
        /// Méthode publique pour permettre à l'OrchestrationService d'initialiser les services optionnels.
        /// Délègue à la méthode privée existante pour préserver la logique.
        /// </summary>
        public void InitializeMigrationServicesPublic()
        {
            InitializeMigrationServices();
        }

        /// <summary>
        /// Méthode publique pour permettre à l'OrchestrationService d'initialiser le service de santé des collections.
        /// Délègue à la méthode privée existante pour préserver la logique.
        /// </summary>
        public void InitializeCollectionHealthServicePublic()
        {
            InitializeCollectionHealthService();
        }

        /// <summary>
        /// Méthode publique pour permettre à l'OrchestrationService d'initialiser l'orchestrateur de changements d'historique.
        /// Reproduit exactement la condition et logique des lignes 292-295 du constructeur original.
        /// </summary>
        public void InitializeHistoryChangeOrchestratorPublic()
        {
            // Reproduire exactement la condition des lignes 292-295
            if (_historyChangeOrchestrator != null && _featureFlagService != null)
            {
                InitializeHistoryChangeOrchestrator(_historyChangeOrchestrator, _featureFlagService);
            }
        }

        /// <summary>
        /// Méthode publique pour permettre à l'EventConfigurationService de configurer les événements de visibilité.
        /// Reproduit exactement la logique des lignes 269-273 du constructeur original.
        /// </summary>
        public void ConfigureVisibilityEventsPublic()
        {
            // Reproduire exactement la logique des lignes 269-273
            if (_visibilityStateManager != null)
            {
                _visibilityStateManager.VisibilityChanged += OnVisibilityStateChanged;
                _loggingService?.LogInfo("Gestionnaire de visibilité configuré");
            }
        }





        #endregion
    }

    /// <summary>
    /// Adaptateur pour convertir GongSolutions.Wpf.DragDrop.IDropInfo vers ClipboardPlus.UI.ViewModels.Managers.Interfaces.IDropInfo
    /// PHASE 5 : Résolution du conflit de types pour DragDropViewModelManager
    /// </summary>
    internal class DropInfoAdapter : ClipboardPlus.UI.ViewModels.Managers.Interfaces.IDropInfo
    {
        private readonly GongSolutions.Wpf.DragDrop.IDropInfo _originalDropInfo;

        public DropInfoAdapter(GongSolutions.Wpf.DragDrop.IDropInfo originalDropInfo)
        {
            _originalDropInfo = originalDropInfo ?? throw new ArgumentNullException(nameof(originalDropInfo));
        }

        public System.Windows.IDataObject Data => (System.Windows.IDataObject)_originalDropInfo.Data;

        public System.Windows.DragDropEffects Effects
        {
            get => _originalDropInfo.Effects;
            set => _originalDropInfo.Effects = value;
        }

        public int InsertIndex => _originalDropInfo.InsertIndex;

        public object? TargetItem => _originalDropInfo.TargetItem;

        public object? TargetCollection => _originalDropInfo.TargetCollection;
    }
}