using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;
using ClipboardPlus.UI.ViewModels;
using ClipboardPlus.UI.Windows;
using Microsoft.Extensions.DependencyInjection;
using System.Windows.Forms;
using MessageBox = System.Windows.MessageBox;
using MessageBoxButton = System.Windows.MessageBoxButton;
using MessageBoxImage = System.Windows.MessageBoxImage;
using WpfListBox = System.Windows.Controls.ListBox;
using WpfKeyEventArgs = System.Windows.Input.KeyEventArgs;
using WpfApplication = System.Windows.Application;
using WpfMessageBox = System.Windows.MessageBox;
using WpfMessageBoxResult = System.Windows.MessageBoxResult;

namespace ClipboardPlus.UI.Controls
{
    /// <summary>
    /// Logique d'interaction pour ClipboardItemControl.xaml
    /// </summary>
    public partial class ClipboardItemControl : System.Windows.Controls.UserControl
    {
        private ClipboardItem? _currentItem;
        private ClipboardHistoryViewModel? _viewModel;
        private bool _propertyChangedHandlerRegistered = false;
        private bool _viewModelSearchAttempted = false; // Pour éviter des recherches répétées qui échouent
        private readonly ILoggingService? _loggingService;

        // ANCIEN SYSTÈME SUPPRIMÉ - Plus de propriétés de dépendance obsolètes
        // Le système SOLID gère maintenant toute la visibilité

        // ANCIEN SYSTÈME SUPPRIMÉ - Plus de propriétés CLR obsolètes
        // Le système SOLID gère maintenant toute la visibilité

        /// <summary>
        /// Initialise une nouvelle instance du contrôle d'élément du presse-papiers.
        /// </summary>
        public ClipboardItemControl()
        {
            InitializeComponent();

            // VÉRIFIER LES RESSOURCES XAML
            try
            {
                var titleConverter = this.FindResource("TitleVisibilityConverter");
            }
            catch (Exception)
            {
                // Ignorer les erreurs de ressources
            }

            try
            {
                var timestampConverter = this.FindResource("TimestampVisibilityConverter");
            }
            catch (Exception)
            {
                // Ignorer les erreurs de ressources
            }

            _loggingService = GetLoggingService();

            // Ajouter un événement pour le chargement du contrôle
            this.Loaded += ClipboardItemControl_Loaded;

            // S'abonner aux changements de DataContext pour mettre à jour l'état de renommage
            DataContextChanged += ClipboardItemControl_DataContextChanged;
            this.Unloaded += ClipboardItemControl_Unloaded;
        }

        private void ClipboardItemControl_Loaded(object sender, RoutedEventArgs e)
        {
            RegisterPropertyChangedHandler();
            CheckRenameState();

            // Réinitialiser le flag de recherche de ViewModel lors du chargement
            _viewModelSearchAttempted = false;

            // Diagnostics additionnels
            var item = DataContext as ClipboardItem;
            if (item != null)
            {
                _currentItem = item;
                _loggingService?.LogInfo($"[CONTRÔLE] ClipboardItemControl chargé pour l'élément {item.Id} - Système SOLID actif");

                // VÉRIFIER LES BINDINGS ET CONVERTISSEURS
                var titleBlock = DisplayNameTextBlock;
                if (titleBlock != null)
                {
                    // VÉRIFIER LE BINDING
                    var binding = System.Windows.Data.BindingOperations.GetBinding(titleBlock, System.Windows.Controls.TextBlock.VisibilityProperty);
                    if (binding != null)
                    {
                        // Binding trouvé
                    }
                    else
                    {
                        // Aucun binding trouvé
                    }

                    _loggingService?.LogInfo($"[CONTRÔLE] TextBlock titre - Visibilité={titleBlock.Visibility}, Texte='{titleBlock.Text}'");
                }
                else
                {
                    // DisplayNameTextBlock est NULL
                }
            }
            else
            {
                // DataContext n'est pas un ClipboardItem
            }
        }

        /// <summary>
        /// Gère le changement de DataContext.
        /// </summary>
        private void ClipboardItemControl_DataContextChanged(object sender, DependencyPropertyChangedEventArgs e)
        {
            Debug.WriteLine("DataContextChanged: " + (e.NewValue?.GetType().Name ?? "null"));

            // DIAGNOSTIC CRITIQUE : Vérifier le DataContext et IsTitleVisible
            System.Diagnostics.Debug.WriteLine($"[DATACONTEXT_DIAGNOSTIC] ClipboardItemControl DataContext changed - Old: {e.OldValue?.GetType().Name}, New: {e.NewValue?.GetType().Name}");
            if (e.NewValue is ClipboardItem item)
            {
                System.Diagnostics.Debug.WriteLine($"[DATACONTEXT_DIAGNOSTIC] ClipboardItem received - ID: {item.Id}, CustomName: '{item.CustomName}', IsTitleVisible: {item.IsTitleVisible}");
            }
            
            // Désabonner de l'ancien item si nécessaire
            if (_currentItem != null && _currentItem is INotifyPropertyChanged oldNotifier)
            {
                oldNotifier.PropertyChanged -= Item_PropertyChanged;
                Debug.WriteLine($"Désabonnement de l'élément {_currentItem.Id}");
            }
            
            // Désabonner de l'ancien ViewModel si nécessaire
            if (_viewModel != null && _viewModel is INotifyPropertyChanged oldVmNotifier)
            {
                oldVmNotifier.PropertyChanged -= ViewModel_PropertyChanged;
                Debug.WriteLine("Désabonnement de l'ancien ViewModel");
                _propertyChangedHandlerRegistered = false; // Réinitialiser pour permettre un nouvel abonnement
            }
            
            // Mettre à jour la référence à l'élément actuel
            _currentItem = DataContext as ClipboardItem;
            
            // Réinitialiser le flag de recherche de ViewModel lors du changement de DataContext
            _viewModelSearchAttempted = false;
            
            // Vérifier si l'élément est en cours de renommage
            RegisterPropertyChangedHandler();
            CheckRenameState();
        }
        
        /// <summary>
        /// Gère le déchargement du contrôle.
        /// </summary>
        private void ClipboardItemControl_Unloaded(object sender, RoutedEventArgs e)
        {
            Debug.WriteLine("ClipboardItemControl_Unloaded: " + (_currentItem?.Id.ToString() ?? "null"));
            // Désabonner des événements pour éviter les fuites de mémoire
            if (_currentItem != null && _currentItem is INotifyPropertyChanged itemNotifier)
            {
                itemNotifier.PropertyChanged -= Item_PropertyChanged;
                Debug.WriteLine($"Désabonnement de l'élément {_currentItem.Id} lors de Unloaded");
            }
            if (_viewModel != null && _viewModel is INotifyPropertyChanged vmNotifier)
            {
                vmNotifier.PropertyChanged -= ViewModel_PropertyChanged;
                Debug.WriteLine("Désabonnement du ViewModel lors de Unloaded");
            }
            _propertyChangedHandlerRegistered = false;
            _viewModel = null; // Libérer la référence au ViewModel
            _currentItem = null; // Libérer la référence à l'item
        }
        
        /// <summary>
        /// S'abonne aux événements PropertyChanged de l'élément actuel et du ViewModel
        /// </summary>
        private void RegisterPropertyChangedHandler()
        {
            // Éviter les abonnements multiples
            if (_propertyChangedHandlerRegistered)
                return;
                
            // S'abonner à l'élément actuel si possible
            if (_currentItem != null && _currentItem is INotifyPropertyChanged itemNotifier)
            {
                itemNotifier.PropertyChanged += Item_PropertyChanged;
                Debug.WriteLine($"Abonnement à l'élément {_currentItem.Id}");
            }
            
            // Trouver et s'abonner au ViewModel
            _viewModel = FindViewModel();
            if (_viewModel != null && _viewModel is INotifyPropertyChanged vmNotifier)
            {
                vmNotifier.PropertyChanged += ViewModel_PropertyChanged;
                Debug.WriteLine("Abonnement au ViewModel");
                _propertyChangedHandlerRegistered = true;
            }
        }
        
        /// <summary>
        /// Gère les changements de propriété de l'élément
        /// </summary>
        private void Item_PropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            Debug.WriteLine($"Item_PropertyChanged: {e.PropertyName}");
            // Si le nom personnalisé change, la liaison de données devrait automatiquement mettre à jour l'affichage
            // La mise à jour manuelle via UpdateTarget() est généralement redondante si INotifyPropertyChanged est correctement implémenté.
            if (e.PropertyName == nameof(ClipboardItem.CustomName))
            {
                // Dispatcher.InvokeAsync(() => {
                // Debug.WriteLine("Mise à jour de l'affichage du nom personnalisé via UpdateTarget (supprimé)");
                // DisplayNameTextBlock.GetBindingExpression(TextBlock.TextProperty)?.UpdateTarget();
                // });
            }
        }
        
        /// <summary>
        /// Gère les changements de propriété du ViewModel
        /// </summary>
        private void ViewModel_PropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            Debug.WriteLine($"ViewModel_PropertyChanged: {e.PropertyName}");
            
            try
            {
                // Si l'élément en cours de renommage change, vérifier l'état de renommage
                if (e.PropertyName == nameof(ClipboardHistoryViewModel.ItemEnRenommage))
                {
                    Dispatcher.InvokeAsync(CheckRenameState);
                }
                // Si la collection d'éléments change, vérifier si notre élément est toujours là
                else if (e.PropertyName == "HistoryItems" && _currentItem != null && _viewModel != null)
                {
                    if (!_viewModel.HistoryItems.Contains(_currentItem))
                    {
                        Debug.WriteLine("L'élément actuel n'est plus dans la collection");
                        // L'élément a été supprimé, rien à faire de spécial ici
                        // Le contrôle sera probablement recyclé/détruit
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"ViewModel_PropertyChanged exception: {ex.Message}");
            }
        }

        /// <summary>
        /// Vérifie si l'élément est en cours de renommage et met à jour l'interface en conséquence.
        /// </summary>
        private void CheckRenameState()
        {
            try 
            {
                // Vérifier que nous avons un élément valide
                if (DataContext is not ClipboardItem item)
                {
                    Debug.WriteLine("CheckRenameState: DataContext is not ClipboardItem");
                    return;
                }
                
                Debug.WriteLine($"CheckRenameState: Item ID={item.Id}, CustomName={item.CustomName ?? "null"}");
                
                // Obtenir le ViewModel si nécessaire
                if (_viewModel == null)
                {
                    _viewModel = FindViewModel();
                    if (_viewModel == null)
                    {
                        Debug.WriteLine("CheckRenameState: ViewModel not found");
                        return;
                    }
                }
                
                // Vérifier si cet élément est en cours de renommage
                bool isRenaming = _viewModel.ItemEnRenommage == item;
                Debug.WriteLine($"CheckRenameState: ItemEnRenommage={_viewModel.ItemEnRenommage?.Id.ToString() ?? "null"}, isRenaming={isRenaming}");
                
                // Simplement mettre à jour la visibilité des contrôles d'édition
                EditNameTextBox.Visibility = isRenaming ? Visibility.Visible : Visibility.Collapsed;

                // === CORRECTION FINALE : NE PAS TOUCHER AU DISPLAYNAME TEXTBLOCK ===
                // On laisse le binding XAML sur DisplayNameTextBlock.Visibility faire TOUT le travail.
                // On ne le force plus à Collapsed ou Visible ici.
                // La seule chose qui le masquera est le fait que le TextBox d'édition,
                // lorsqu'il est visible, se superposera au TextBlock.
                // Ceci est géré par la disposition des contrôles dans le XAML.

                if (isRenaming)
                {
                    EditNameTextBox.Text = _viewModel.NouveauNom;
                    Dispatcher.BeginInvoke(new Action(() => {
                        try
                        {
                            EditNameTextBox.Focus();
                            EditNameTextBox.SelectAll();
                            Debug.WriteLine("CheckRenameState: Focus and selection set");
                        }
                        catch (Exception ex)
                        {
                            Debug.WriteLine($"CheckRenameState focus exception: {ex.Message}");
                        }
                    }), System.Windows.Threading.DispatcherPriority.Input);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"CheckRenameState exception: {ex.Message}");
            }
        }

        /// <summary>
        /// Trouve le ViewModel parent avec une mise en cache pour éviter des recherches répétées.
        /// </summary>
        private ClipboardHistoryViewModel? FindViewModel()
        {
            try
            {
                // Si nous avons déjà un ViewModel valide, le retourner directement
                if (_viewModel != null)
                {
                    return _viewModel;
                }
                
                // Si nous avons déjà essayé de trouver le ViewModel sans succès, ne pas réessayer
                // sauf si explicitement demandé (en réinitialisant _viewModelSearchAttempted)
                if (_viewModelSearchAttempted)
                {
                    Debug.WriteLine("FindViewModel: Recherche déjà tentée sans succès, abandon");
                    return null;
                }
                
                Debug.WriteLine("FindViewModel: Recherche du ViewModel...");
                
                // Méthode 1: Chercher le ListBox parent qui contient le contrôle (méthode la plus fiable)
                var listBox = this.FindParentOfType<WpfListBox>();
                if (listBox != null)
                {
                    var viewModel = listBox.DataContext as ClipboardHistoryViewModel;
                    if (viewModel != null)
                    {
                        Debug.WriteLine("FindViewModel: ViewModel trouvé via ListBox parent");
                        _viewModel = viewModel;
                        return viewModel;
                    }
                }
                
                // Méthode 2: Chercher dans les ancêtres visuels
                DependencyObject? current = this;
                int searchDepth = 0;
                const int MAX_SEARCH_DEPTH = 20; // Éviter les recherches infinies
                
                while (current != null && searchDepth < MAX_SEARCH_DEPTH)
                {
                    searchDepth++;
                    
                    if (current is FrameworkElement fe && fe.DataContext is ClipboardHistoryViewModel feVm)
                    {
                        Debug.WriteLine($"FindViewModel: ViewModel trouvé via ancêtre visuel (profondeur: {searchDepth})");
                        _viewModel = feVm;
                        return feVm;
                    }
                    
                    // Essayer de trouver le ListBox dans l'arbre visuel
                    if (current is WpfListBox lb && lb.DataContext is ClipboardHistoryViewModel lbVm)
                    {
                        Debug.WriteLine($"FindViewModel: ViewModel trouvé via ListBox dans l'arbre visuel (profondeur: {searchDepth})");
                        _viewModel = lbVm;
                        return lbVm;
                    }
                    
                    current = VisualTreeHelper.GetParent(current);
                }
                
                if (searchDepth >= MAX_SEARCH_DEPTH)
                {
                    Debug.WriteLine("FindViewModel: Profondeur de recherche maximale atteinte");
                }
                
                // Méthode 3: Chercher via la fenêtre principale
                if (WpfApplication.Current?.MainWindow?.DataContext is ClipboardHistoryViewModel mainVm)
                {
                    Debug.WriteLine("FindViewModel: ViewModel trouvé via MainWindow");
                    _viewModel = mainVm;
                    return mainVm;
                }
                
                // Méthode 4: Chercher via le service provider (si disponible)
                try {
                    if (WpfApplication.Current != null)
                    {
                        var serviceProvider = WpfApplication.Current.GetType().GetProperty("Services")?.GetValue(WpfApplication.Current) as IServiceProvider;
                        if (serviceProvider != null)
                        {
                            var vm = serviceProvider.GetService(typeof(ClipboardHistoryViewModel)) as ClipboardHistoryViewModel;
                            if (vm != null)
                            {
                                Debug.WriteLine("FindViewModel: ViewModel trouvé via ServiceProvider");
                                _viewModel = vm;
                                return vm;
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"FindViewModel: Erreur lors de la recherche via ServiceProvider: {ex.Message}");
                }
                
                // Marquer que nous avons essayé de trouver le ViewModel
                _viewModelSearchAttempted = true;
                Debug.WriteLine("FindViewModel: Aucun ViewModel trouvé");
                return null;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"FindViewModel exception: {ex.Message}");
                _viewModelSearchAttempted = true;
                return null;
            }
        }

        /// <summary>
        /// Gère les touches pressées dans le TextBox d'édition.
        /// </summary>
        private void EditNameTextBox_KeyDown(object sender, WpfKeyEventArgs e)
        {
            try
            {
                Debug.WriteLine($"EditNameTextBox_KeyDown: Key={e.Key}");
                
                // Obtenir le ViewModel si nécessaire
                if (_viewModel == null)
                {
                    _viewModel = FindViewModel();
                    if (_viewModel == null)
                    {
                        Debug.WriteLine("EditNameTextBox_KeyDown: ViewModel not found");
                        RenamingDiagnostic.LogRenameResult(false, "ViewModel non trouvé dans EditNameTextBox_KeyDown");
                        return;
                    }
                }
                
                // Gérer les touches spéciales
                if (e.Key == Key.Enter)
                {
                    // Journaliser l'état avant confirmation
                    RenamingDiagnostic.LogRenameConfirmAttempt(_viewModel, EditNameTextBox);
                    
                    // Confirmer le renommage
                    Debug.WriteLine("EditNameTextBox_KeyDown: Executing ConfirmerRenommageCommand");
                    if (_viewModel.ConfirmerRenommageCommand.CanExecute(null))
                    {
                        try
                        {
                            _viewModel.ConfirmerRenommageCommand.Execute(null);
                            RenamingDiagnostic.LogRenameResult(true, "Commande exécutée avec succès via Enter");
                            
                            // Déplacer le focus pour éviter le rectangle en pointillés
                            try
                            {
                                Debug.WriteLine("EditNameTextBox_KeyDown: Déplacement du focus pour éviter le rectangle en pointillés");
                                
                                // Trouver la fenêtre parente
                                var window = Window.GetWindow(this);
                                if (window != null)
                                {
                                    // Donner le focus à la fenêtre elle-même
                                    window.Focus();
                                    e.Handled = true;
                                    
                                    // Journaliser l'état du focus après déplacement
                                    var historyListBox = ControlExtensions.FindParentOfType<WpfListBox>(this);
                                    RenamingDiagnostic.LogFocusState(Keyboard.FocusedElement as UIElement, historyListBox);
                                }
                            }
                            catch (Exception focusEx)
                            {
                                Debug.WriteLine($"EditNameTextBox_KeyDown: Erreur lors du déplacement du focus: {focusEx.Message}");
                            }
                        }
                        catch (Exception cmdEx)
                        {
                            RenamingDiagnostic.LogRenameException(cmdEx, "Exécution de ConfirmerRenommageCommand via Enter");
                        }
                    }
                    else
                    {
                        RenamingDiagnostic.LogRenameResult(false, "ConfirmerRenommageCommand.CanExecute a retourné false");
                    }
                    e.Handled = true;
                }
                else if (e.Key == Key.Escape)
                {
                    // Annuler le renommage
                    Debug.WriteLine("EditNameTextBox_KeyDown: Executing AnnulerRenommageCommand");
                    if (_viewModel.AnnulerRenommageCommand.CanExecute(null))
                    {
                        try
                        {
                            _viewModel.AnnulerRenommageCommand.Execute(null);
                            RenamingDiagnostic.LogRenameResult(true, "Commande d'annulation exécutée avec succès via Escape");
                        }
                        catch (Exception cmdEx)
                        {
                            RenamingDiagnostic.LogRenameException(cmdEx, "Exécution de AnnulerRenommageCommand via Escape");
                        }
                    }
                    else
                    {
                        RenamingDiagnostic.LogRenameResult(false, "AnnulerRenommageCommand.CanExecute a retourné false");
                    }
                    e.Handled = true;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"EditNameTextBox_KeyDown exception: {ex.Message}");
                RenamingDiagnostic.LogRenameException(ex, "Méthode EditNameTextBox_KeyDown");
            }
        }

        /// <summary>
        /// Gère la perte de focus du TextBox d'édition.
        /// </summary>
        private void EditNameTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            try
            {
                Debug.WriteLine("EditNameTextBox_LostFocus");
                
                // Obtenir le ViewModel si nécessaire
                if (_viewModel == null)
                {
                    _viewModel = FindViewModel();
                    if (_viewModel == null)
                    {
                        Debug.WriteLine("EditNameTextBox_LostFocus: ViewModel not found");
                        RenamingDiagnostic.LogRenameResult(false, "ViewModel non trouvé dans EditNameTextBox_LostFocus");
                        return;
                    }
                }
                
                // Journaliser l'état avant confirmation
                RenamingDiagnostic.LogRenameConfirmAttempt(_viewModel, EditNameTextBox);
                
                // Confirmer le renommage automatiquement à la perte de focus
                Debug.WriteLine("EditNameTextBox_LostFocus: Executing ConfirmerRenommageCommand");
                if (_viewModel.ConfirmerRenommageCommand.CanExecute(null))
                {
                    try
                    {
                        _viewModel.ConfirmerRenommageCommand.Execute(null);
                        RenamingDiagnostic.LogRenameResult(true, "Commande exécutée avec succès via LostFocus");
                    }
                    catch (Exception cmdEx)
                    {
                        RenamingDiagnostic.LogRenameException(cmdEx, "Exécution de ConfirmerRenommageCommand via LostFocus");
                    }
                }
                else
                {
                    RenamingDiagnostic.LogRenameResult(false, "ConfirmerRenommageCommand.CanExecute a retourné false dans LostFocus");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"EditNameTextBox_LostFocus exception: {ex.Message}");
                RenamingDiagnostic.LogRenameException(ex, "Méthode EditNameTextBox_LostFocus");
            }
        }

        /// <summary>
        /// Méthode de diagnostic pour tester directement les commandes du ViewModel
        /// </summary>
        private void TestViewModelCommands()
        {
            try
            {
                if (_currentItem == null)
                {
                    Debug.WriteLine("TestViewModelCommands: _currentItem est null");
                    WpfMessageBox.Show("L'élément actuel est null", "Diagnostic", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }
                
                var vm = FindViewModel();
                if (vm == null)
                {
                    Debug.WriteLine("TestViewModelCommands: ViewModel non trouvé");
                    WpfMessageBox.Show("ViewModel non trouvé", "Diagnostic", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }
                
                // Tester les commandes
                Debug.WriteLine($"TestViewModelCommands: ViewModel trouvé, testing commands");
                
                // Vérifier si les commandes sont initialisées
                if (vm.DemarrerRenommageCommand == null)
                {
                    Debug.WriteLine("TestViewModelCommands: DemarrerRenommageCommand est null");
                    WpfMessageBox.Show("DemarrerRenommageCommand est null", "Diagnostic", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }
                
                // Vérifier si la commande peut être exécutée
                bool canExecute = vm.DemarrerRenommageCommand.CanExecute(_currentItem);
                Debug.WriteLine($"TestViewModelCommands: DemarrerRenommageCommand.CanExecute = {canExecute}");
                
                // Exécuter la commande directement
                if (canExecute)
                {
                    Debug.WriteLine("TestViewModelCommands: Executing DemarrerRenommageCommand");
                    vm.DemarrerRenommageCommand.Execute(_currentItem);
                    WpfMessageBox.Show("Commande exécutée avec succès", "Diagnostic", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    Debug.WriteLine("TestViewModelCommands: Cannot execute DemarrerRenommageCommand");
                    WpfMessageBox.Show("La commande ne peut pas être exécutée", "Diagnostic", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"TestViewModelCommands exception: {ex.Message}");
                WpfMessageBox.Show($"Erreur: {ex.Message}", "Diagnostic", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Gestionnaire d'événement pour le clic sur l'élément de menu "Renommer"
        /// </summary>
        private void RenameMenuItem_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                Debug.WriteLine("RenameMenuItem_Click");
                if (_currentItem == null)
                {
                    Debug.WriteLine("RenameMenuItem_Click: _currentItem est null");
                    RenamingDiagnostic.LogRenameResult(false, "L'élément actuel est null dans RenameMenuItem_Click");
                    ShowFeedback(false, "Impossible de renommer : élément non disponible");
                    return;
                }
                
                var vm = FindViewModel();
                if (vm == null)
                {
                    Debug.WriteLine("RenameMenuItem_Click: ViewModel non trouvé");
                    RenamingDiagnostic.LogRenameResult(false, "ViewModel non trouvé dans RenameMenuItem_Click");
                    ShowFeedback(false, "Impossible de renommer : contexte non disponible");
                    return;
                }
                
                // Journaliser l'état avant de démarrer le renommage
                RenamingDiagnostic.LogRenameStart(_currentItem, vm);
                
                if (vm.DemarrerRenommageCommand.CanExecute(_currentItem))
                {
                    Debug.WriteLine("RenameMenuItem_Click: Executing DemarrerRenommageCommand");
                    try
                    {
                        vm.DemarrerRenommageCommand.Execute(_currentItem);
                        RenamingDiagnostic.LogRenameResult(true, "Commande de démarrage de renommage exécutée avec succès");
                        
                        // Feedback visuel
                        ShowFeedback(true);
                    }
                    catch (Exception cmdEx)
                    {
                        RenamingDiagnostic.LogRenameException(cmdEx, "Exécution de DemarrerRenommageCommand");
                        ShowFeedback(false, "Erreur lors du démarrage du renommage");
                    }
                }
                else
                {
                    Debug.WriteLine("RenameMenuItem_Click: Cannot execute DemarrerRenommageCommand");
                    RenamingDiagnostic.LogRenameResult(false, "DemarrerRenommageCommand.CanExecute a retourné false");
                    ShowFeedback(false, "Impossible de démarrer le renommage pour le moment");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"RenameMenuItem_Click exception: {ex.Message}");
                RenamingDiagnostic.LogRenameException(ex, "Méthode RenameMenuItem_Click");
                ShowFeedback(false, "Erreur lors du renommage");
            }
        }
        
        /// <summary>
        /// Gestionnaire d'événement pour le clic sur l'élément de menu "Épingler/Désépingler"
        /// </summary>
        private void PinMenuItem_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                Debug.WriteLine("PinMenuItem_Click");
                if (_currentItem == null)
                {
                    Debug.WriteLine("PinMenuItem_Click: _currentItem est null");
                    ShowFeedback(false, "Impossible d'épingler : élément non disponible");
                    return;
                }
                
                var vm = FindViewModel();
                if (vm == null)
                {
                    Debug.WriteLine("PinMenuItem_Click: ViewModel non trouvé");
                    ShowFeedback(false, "Impossible d'épingler : contexte non disponible");
                    return;
                }
                
                if (vm.BasculerEpinglageCommand.CanExecute(_currentItem))
                {
                    Debug.WriteLine("PinMenuItem_Click: Executing BasculerEpinglageCommand");
                    vm.BasculerEpinglageCommand.Execute(_currentItem);
                    
                    // Feedback visuel
                    ShowFeedback(true, _currentItem.IsPinned ? "Élément épinglé" : "Élément désépinglé");
                }
                else
                {
                    Debug.WriteLine("PinMenuItem_Click: Cannot execute BasculerEpinglageCommand");
                    ShowFeedback(false, "Impossible de modifier l'épinglage pour le moment");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"PinMenuItem_Click exception: {ex.Message}");
                ShowFeedback(false, "Erreur lors de la modification de l'épinglage");
            }
        }
        
        /// <summary>
        /// Gestionnaire d'événement pour le clic sur l'élément de menu "Prévisualiser"
        /// </summary>
        private void PreviewMenuItem_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                Debug.WriteLine("PreviewMenuItem_Click");
                if (_currentItem == null)
                {
                    Debug.WriteLine("PreviewMenuItem_Click: _currentItem est null");
                    ShowFeedback(false, "Impossible de prévisualiser : élément non disponible");
                    return;
                }
                
                var vm = FindViewModel();
                if (vm == null)
                {
                    Debug.WriteLine("PreviewMenuItem_Click: ViewModel non trouvé");
                    ShowFeedback(false, "Impossible de prévisualiser : contexte non disponible");
                    return;
                }
                
                if (vm.AfficherPreviewCommand.CanExecute(_currentItem))
                {
                    Debug.WriteLine("PreviewMenuItem_Click: Executing AfficherPreviewCommand");
                    vm.AfficherPreviewCommand.Execute(_currentItem);
                    
                    // Pas de feedback visuel ici car la fenêtre de prévisualisation s'ouvre déjà
                }
                else
                {
                    Debug.WriteLine("PreviewMenuItem_Click: Cannot execute AfficherPreviewCommand");
                    ShowFeedback(false, "Impossible de prévisualiser pour le moment");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"PreviewMenuItem_Click exception: {ex.Message}");
                ShowFeedback(false, "Erreur lors de la prévisualisation");
            }
        }

        /// <summary>
        /// Gestionnaire d'événement pour le clic sur l'élément de menu "Nettoyage avancé"
        /// </summary>
        private void AdvancedCleanupMenuItem_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                Debug.WriteLine("AdvancedCleanupMenuItem_Click");

                var vm = FindViewModel();
                if (vm == null)
                {
                    Debug.WriteLine("AdvancedCleanupMenuItem_Click: ViewModel non trouvé");
                    ShowFeedback(false, "Impossible d'ouvrir le nettoyage avancé : contexte non disponible");
                    return;
                }

                if (vm.OpenAdvancedCleanupCommand.CanExecute(null))
                {
                    Debug.WriteLine("AdvancedCleanupMenuItem_Click: Executing OpenAdvancedCleanupCommand");
                    vm.OpenAdvancedCleanupCommand.Execute(null);
                }
                else
                {
                    Debug.WriteLine("AdvancedCleanupMenuItem_Click: Cannot execute OpenAdvancedCleanupCommand");
                    ShowFeedback(false, "Impossible d'ouvrir le nettoyage avancé pour le moment");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"AdvancedCleanupMenuItem_Click exception: {ex.Message}");
                ShowFeedback(false, "Erreur lors de l'ouverture du nettoyage avancé");
            }
        }

        /// <summary>
        /// Gestionnaire d'événement pour le clic sur l'élément de menu "Supprimer"
        /// </summary>
        private void DeleteMenuItem_Click(object sender, RoutedEventArgs e)
        {
            // Journaliser le début de la suppression
            _loggingService?.LogInfo($"[DEBUT] DeleteMenuItem_Click - ThreadID: {Environment.CurrentManagedThreadId}");

            try
            {
                // Vérifier si l'élément actuel est null
                if (_currentItem == null)
                {
                    _loggingService?.LogWarning("DeleteMenuItem_Click: _currentItem est null, sortie prématurée");
                    
                    // Informer l'utilisateur
                    WpfMessageBox.Show("Impossible de supprimer l'élément: aucun élément sélectionné.",
                                    "ClipboardPlus - Erreur",
                                    MessageBoxButton.OK,
                                    MessageBoxImage.Warning);
                    return;
                }

                // Journaliser les détails de l'élément à supprimer
                Debug.WriteLine($"DeleteMenuItem_Click: Suppression de l'élément - ID: {_currentItem.Id}, Type: {_currentItem.DataType}, Nom: {_currentItem.CustomName ?? "(non défini)"}");
                
                // Récupérer le ViewModel
                var viewModel = FindViewModel();
                
                // Vérifier si le ViewModel est null
                if (viewModel == null)
                {
                    string errorMsg = "DeleteMenuItem_Click: Impossible de trouver le ViewModel";
                    _loggingService?.LogError(errorMsg);
                    
                    // Informer l'utilisateur
                    WpfMessageBox.Show("Impossible de supprimer l'élément: contexte de données invalide.",
                                    "ClipboardPlus - Erreur",
                                    MessageBoxButton.OK,
                                    MessageBoxImage.Error);
                    return;
                }
                
                // Journaliser le type du ViewModel
                Debug.WriteLine($"DeleteMenuItem_Click: Type du ViewModel: {viewModel.GetType().FullName}");

                // Vérifier si la commande SupprimerElementCommand existe
                if (viewModel.SupprimerElementCommand == null)
                {
                    string errorMsg = "DeleteMenuItem_Click: La commande SupprimerElementCommand est null";
                    _loggingService?.LogError(errorMsg);
                    
                    // Informer l'utilisateur
                    WpfMessageBox.Show("Impossible de supprimer l'élément: commande de suppression non disponible.",
                                    "ClipboardPlus - Erreur",
                                    MessageBoxButton.OK,
                                    MessageBoxImage.Error);
                    return;
                }
                
                // Vérifier si la commande peut être exécutée
                bool canExecute = viewModel.SupprimerElementCommand.CanExecute(_currentItem);
                Debug.WriteLine($"DeleteMenuItem_Click: SupprimerElementCommand.CanExecute: {canExecute}");
                
                if (!canExecute)
                {
                    string errorMsg = "DeleteMenuItem_Click: La commande SupprimerElementCommand ne peut pas être exécutée";
                    _loggingService?.LogError(errorMsg);
                    
                    // Informer l'utilisateur
                    WpfMessageBox.Show("Impossible de supprimer l'élément: l'opération n'est pas autorisée actuellement.",
                                    "ClipboardPlus - Erreur",
                                    MessageBoxButton.OK,
                                    MessageBoxImage.Warning);
                    return;
                }

                // Exécuter la commande de suppression
                _loggingService?.LogInfo($"DeleteMenuItem_Click: Exécution de SupprimerElementCommand pour l'élément {_currentItem.Id}");
                
                // Vérifier que l'élément est toujours dans la collection avant de tenter de le supprimer
                bool itemExists = viewModel.HistoryItems.Contains(_currentItem);
                Debug.WriteLine($"DeleteMenuItem_Click: L'élément existe dans la collection: {itemExists}");
                
                // Vérifier par ID également
                var foundItem = viewModel.HistoryItems.FirstOrDefault(i => i.Id == _currentItem.Id);
                Debug.WriteLine($"DeleteMenuItem_Click: Élément trouvé par ID: {(foundItem != null ? "Oui" : "Non")}");
                
                // Exécuter la commande
                viewModel.SupprimerElementCommand.Execute(_currentItem);
                
                _loggingService?.LogInfo($"DeleteMenuItem_Click: Commande SupprimerElementCommand exécutée pour l'élément {_currentItem.Id}");
                
                // Vérifier si l'élément est toujours dans la collection après la suppression
                bool itemStillExists = viewModel.HistoryItems.Contains(_currentItem);
                Debug.WriteLine($"DeleteMenuItem_Click: L'élément existe toujours dans la collection après suppression: {itemStillExists}");
                
                // Vérifier par ID également
                var stillFoundItem = viewModel.HistoryItems.FirstOrDefault(i => i.Id == _currentItem.Id);
                Debug.WriteLine($"DeleteMenuItem_Click: Élément toujours trouvé par ID après suppression: {(stillFoundItem != null ? "Oui" : "Non")}");
                
                // Purger les éléments orphelins de la base de données
                _loggingService?.LogInfo("DeleteMenuItem_Click: Lancement de la purge des éléments orphelins");
                
                // Utiliser Task.Run pour ne pas bloquer l'interface utilisateur
                Task.Run(async () =>
                {
                    try
                    {
                        int deletedCount = await viewModel.PurgeOrphanedItemsAsync();
                        
                        _loggingService?.LogInfo($"DeleteMenuItem_Click: Purge terminée, {deletedCount} élément(s) orphelin(s) supprimé(s)");
                    }
                    catch (Exception purgeEx)
                    {
                        _loggingService?.LogError($"DeleteMenuItem_Click: Erreur lors de la purge: {purgeEx.Message}", purgeEx);
                    }
                });
                
                // Journaliser la fin de la suppression
                _loggingService?.LogInfo($"[FIN] DeleteMenuItem_Click - Suppression réussie pour l'élément {_currentItem.Id}");
            }
            catch (Exception ex)
            {
                string errorMsg = $"DeleteMenuItem_Click: Erreur lors de la suppression: {ex.Message}";
                _loggingService?.LogError(errorMsg, ex);
                
                // Informer l'utilisateur
                WpfMessageBox.Show($"Erreur lors de la suppression de l'élément: {ex.Message}",
                                "ClipboardPlus - Erreur",
                                MessageBoxButton.OK,
                                MessageBoxImage.Error);
                
                _loggingService?.LogInfo($"[FIN] DeleteMenuItem_Click - Échec: {ex.Message}");
            }
        }

        /// <summary>
        /// Gestionnaire d'événement pour le menu "Supprimer tout".
        /// Supprime tous les éléments de l'historique sauf les épinglés.
        /// </summary>
        private async void DeleteAllMenuItem_Click(object sender, RoutedEventArgs e)
        {
            string operationId = Guid.NewGuid().ToString("N").Substring(0, 8);
            _loggingService?.LogInfo($"[{operationId}] DeleteAllMenuItem_Click: Début de l'opération");

            try
            {
                // Obtenir le ViewModel depuis le DataContext de la fenêtre parente
                var viewModel = FindViewModel();
                if (viewModel == null)
                {
                    string errorMsg = $"[{operationId}] DeleteAllMenuItem_Click: Impossible d'obtenir le ViewModel";
                    _loggingService?.LogError(errorMsg);

                    WpfMessageBox.Show("Impossible d'accéder aux données de l'historique.",
                                    "ClipboardPlus - Erreur",
                                    MessageBoxButton.OK,
                                    MessageBoxImage.Error);
                    return;
                }

                // Vérifier si la commande SupprimerToutCommand existe
                if (viewModel.SupprimerToutCommand == null)
                {
                    string errorMsg = $"[{operationId}] DeleteAllMenuItem_Click: La commande SupprimerToutCommand est null";
                    _loggingService?.LogError(errorMsg);

                    WpfMessageBox.Show("Impossible de supprimer tous les éléments: commande non disponible.",
                                    "ClipboardPlus - Erreur",
                                    MessageBoxButton.OK,
                                    MessageBoxImage.Error);
                    return;
                }

                // Vérifier si la commande peut être exécutée
                bool canExecute = viewModel.SupprimerToutCommand.CanExecute(null);
                Debug.WriteLine($"[{operationId}] DeleteAllMenuItem_Click: SupprimerToutCommand.CanExecute: {canExecute}");

                if (!canExecute)
                {
                    string errorMsg = $"[{operationId}] DeleteAllMenuItem_Click: La commande SupprimerToutCommand ne peut pas être exécutée";
                    _loggingService?.LogInfo(errorMsg);

                    WpfMessageBox.Show("Impossible de supprimer tous les éléments: l'opération n'est pas autorisée actuellement.",
                                    "ClipboardPlus - Information",
                                    MessageBoxButton.OK,
                                    MessageBoxImage.Information);
                    return;
                }

                // Exécuter la commande de suppression de tous les éléments
                _loggingService?.LogInfo($"[{operationId}] DeleteAllMenuItem_Click: Exécution de SupprimerToutCommand");

                await viewModel.SupprimerToutCommand.ExecuteAsync(null);

                _loggingService?.LogInfo($"[{operationId}] DeleteAllMenuItem_Click: Commande SupprimerToutCommand exécutée avec succès");
            }
            catch (Exception ex)
            {
                string errorMsg = $"[{operationId}] DeleteAllMenuItem_Click: Erreur lors de la suppression: {ex.Message}";
                _loggingService?.LogError(errorMsg, ex);

                WpfMessageBox.Show($"Erreur lors de la suppression de tous les éléments: {ex.Message}",
                                "ClipboardPlus - Erreur",
                                MessageBoxButton.OK,
                                MessageBoxImage.Error);
            }
            finally
            {
                _loggingService?.LogInfo($"[{operationId}] DeleteAllMenuItem_Click: Fin de l'opération");
            }
        }

        /// <summary>
        /// Affiche un feedback visuel à l'utilisateur
        /// </summary>
        /// <param name="success">Indique si l'opération a réussi</param>
        /// <param name="message">Message optionnel à afficher</param>
        private void ShowFeedback(bool success, string? message = null)
        {
            try
            {
                // Animation visuelle sur le contrôle
                if (success)
                {
                    // Animation de succès (légère pulsation verte)
                    var originalBackground = this.Background;
                    var successBrush = new SolidColorBrush(System.Windows.Media.Color.FromArgb(40, 0, 200, 0));
                    
                    this.Background = successBrush;
                    
                    // Restaurer l'arrière-plan d'origine après un délai
                    var timer = new System.Windows.Threading.DispatcherTimer
                    {
                        Interval = TimeSpan.FromMilliseconds(300)
                    };
                    
                    timer.Tick += (s, e) =>
                    {
                        this.Background = originalBackground;
                        timer.Stop();
                    };
                    
                    timer.Start();
                }
                else
                {
                    // Animation d'échec (légère pulsation rouge)
                    var originalBackground = this.Background;
                    var errorBrush = new SolidColorBrush(System.Windows.Media.Color.FromArgb(40, 200, 0, 0));
                    
                    this.Background = errorBrush;
                    
                    // Restaurer l'arrière-plan d'origine après un délai
                    var timer = new System.Windows.Threading.DispatcherTimer
                    {
                        Interval = TimeSpan.FromMilliseconds(300)
                    };
                    
                    timer.Tick += (s, e) =>
                    {
                        this.Background = originalBackground;
                        timer.Stop();
                    };
                    
                    timer.Start();
                    
                    // Afficher le message d'erreur si fourni
                    if (!string.IsNullOrEmpty(message))
                    {
                        Debug.WriteLine($"ShowFeedback: {message}");
                        
                        // Option 1: Afficher dans une infobulle (tooltip)
                        ToolTip = message;
                        
                        // Réinitialiser l'infobulle après un délai
                        var tooltipTimer = new System.Windows.Threading.DispatcherTimer
                        {
                            Interval = TimeSpan.FromSeconds(3)
                        };
                        
                        tooltipTimer.Tick += (s, e) =>
                        {
                            ToolTip = null;
                            tooltipTimer.Stop();
                        };
                        
                        tooltipTimer.Start();
                        
                        // Option 2: On pourrait aussi utiliser un système de notification global
                        // si l'application en dispose
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"ShowFeedback exception: {ex.Message}");
                // Ne rien faire de plus pour éviter une boucle d'erreurs
            }
        }

        // ANCIEN SYSTÈME TOTALEMENT SUPPRIMÉ
        // OnHideItemTitleChanged et UpdateTitleVisibility supprimés
        // Le système SOLID gère maintenant toute la visibilité

        /// <summary>
        /// Récupère le service de logging depuis les ressources de l'application.
        /// </summary>
        private ILoggingService? GetLoggingService()
        {
            // Essayer de récupérer le service de logging depuis l'application
            if (WpfApplication.Current?.Resources["LoggingService"] is ILoggingService loggingService)
            {
                Debug.WriteLine("ClipboardItemControl: Service de logging récupéré depuis les ressources de l'application");
                return loggingService;
            }
            return null;
        }
    }

    /// <summary>
    /// Extensions pour les contrôles WPF.
    /// </summary>
    public static class ControlExtensions
    {
        /// <summary>
        /// Trouve un parent du type spécifié.
        /// </summary>
        /// <typeparam name="T">Type du parent à trouver.</typeparam>
        /// <param name="element">Élément à partir duquel chercher.</param>
        /// <returns>Le parent trouvé ou null.</returns>
        public static T? FindParentOfType<T>(this DependencyObject element) where T : DependencyObject
        {
            DependencyObject? parent = VisualTreeHelper.GetParent(element);
            
            if (parent == null)
                return null;
                
            if (parent is T typedParent)
                return typedParent;
                
            return FindParentOfType<T>(parent);
        }
    }
}
