using System;
using System.Runtime.InteropServices;
using System.Windows;
using System.Windows.Interop;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Services.Interfaces;
using System.Threading;
using System.Windows.Threading;
using System.Threading.Tasks;
using Timer = System.Threading.Timer;

namespace ClipboardPlus.Services
{
    /// <summary>
    /// Service d'écoute du presse-papier utilisant l'API AddClipboardFormatListener
    /// </summary>
    public class ClipboardListenerService : IClipboardListenerService, IDisposable
    {
        #region Champs privés
        
        private readonly ILoggingService _loggingService;
        private readonly IDispatcherService _dispatcherService;
        private readonly ClipboardListenerOptions _options;
        private volatile bool _isListening;
        private IntPtr _windowHandle;
        private HwndSource? _source;
        private Window? _hiddenWindow;
        private bool _isDisposed = false;
        private readonly object _lock = new object();
        private int _startupAttempts = 0;
        private Timer? _reconnectTimer;

        // Debouncing
        private DateTime _lastEventTime = DateTime.MinValue;
        private readonly TimeSpan _debounceInterval;
        private string _lastEventId = string.Empty;

        // Health monitoring
        private Timer? _healthCheckTimer;
        private int _consecutiveHealthCheckFailures = 0;
        private DateTime _lastSuccessfulOperation = DateTime.Now;
        
        // Constantes Windows pour la surveillance du presse-papiers
        private const int WM_CLIPBOARDUPDATE = 0x031D;
        private const int ERROR_INVALID_WINDOW_HANDLE = 1400;
        private const int ERROR_INVALID_PARAMETER = 87;
        
        // Événement interne pour éviter les fuites mémoire
        private EventHandler? _clipboardContentChangedHandler;
        
        #endregion
        
        #region Imports P/Invoke
        
        [DllImport("user32.dll", SetLastError = true)]
        [return: MarshalAs(UnmanagedType.Bool)]
        private static extern bool AddClipboardFormatListener(IntPtr hwnd);

        [DllImport("user32.dll", SetLastError = true)]
        [return: MarshalAs(UnmanagedType.Bool)]
        private static extern bool RemoveClipboardFormatListener(IntPtr hwnd);
        
        [DllImport("user32.dll", SetLastError = true)]
        private static extern bool IsWindow(IntPtr hWnd);
        
        #endregion
        
        #region Événements
        
        /// <summary>
        /// Événement déclenché lorsque le contenu du presse-papier a changé
        /// </summary>
        public event EventHandler? ClipboardContentChanged
        {
            add
            {
                lock (_lock)
                {
                    _clipboardContentChangedHandler += value;
                    _loggingService.LogInfo("ClipboardContentChanged: Gestionnaire d'événement ajouté");
                }
            }
            remove
            {
                lock (_lock)
                {
                    _clipboardContentChangedHandler -= value;
                    _loggingService.LogInfo("ClipboardContentChanged: Gestionnaire d'événement retiré");
                }
            }
        }
        
        #endregion
        
        #region Propriétés
        
        /// <summary>
        /// Indique si le service est actuellement en écoute du presse-papier
        /// </summary>
        public bool IsListening => _isListening;
        
        #endregion
        
        #region Constructeur
        
        /// <summary>
        /// Crée une nouvelle instance du service d'écoute du presse-papier
        /// </summary>
        /// <param name="loggingService">Service de journalisation</param>
        /// <param name="dispatcherService">Service de dispatcher UI</param>
        /// <param name="options">Options de configuration</param>
        public ClipboardListenerService(
            ILoggingService loggingService,
            IDispatcherService dispatcherService,
            ClipboardListenerOptions? options = null)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            _dispatcherService = dispatcherService ?? throw new ArgumentNullException(nameof(dispatcherService));
            _options = options ?? new ClipboardListenerOptions();
            _debounceInterval = TimeSpan.FromMilliseconds(_options.DebounceIntervalMs);
            _isListening = false;
            _windowHandle = IntPtr.Zero;
            
            _loggingService.LogInfo($"ClipboardListenerService: Créé avec options - DebounceInterval: {_options.DebounceIntervalMs}ms, RetryCount: {_options.StartupRetryCount}, RetryDelay: {_options.StartupRetryDelayMs}ms");
        }
        
        #endregion
        
        #region Méthodes publiques
        
        /// <summary>
        /// Démarre l'écoute du presse-papier
        /// </summary>
        /// <returns>True si l'écoute a démarré avec succès, sinon False</returns>
        public virtual bool StartListening()
        {
            lock (_lock)
            {
                if (_isDisposed)
                {
                    _loggingService.LogError("StartListening: Tentative d'utilisation d'un service déjà libéré");
                    throw new ObjectDisposedException(nameof(ClipboardListenerService));
                }
                    
                if (_isListening)
                {
                    _loggingService.LogInfo("StartListening: Le service est déjà en écoute");
                    return true;
                }
                
                _loggingService.LogInfo("StartListening: Démarrage de l'écoute");
                _startupAttempts = 0;
                
                return TryStartListening();
            }
        }
        
        private bool TryStartListening()
        {
            if (_startupAttempts >= _options.StartupRetryCount)
            {
                _loggingService.LogError($"TryStartListening: Échec après {_startupAttempts} tentatives - ABANDON DÉFINITIF");
                return false;
            }

            _startupAttempts++;
            _loggingService.LogInfo($"TryStartListening: Tentative {_startupAttempts}/{_options.StartupRetryCount}");

            // Utilisation du service dispatcher avec choix du mode (bloquant ou non)
            if (_options.UseBlockingUIOperations)
            {
                // Mode bloquant - plus simple mais peut bloquer le thread appelant
                bool result = _dispatcherService.Invoke(() => InitializeWindow());
                if (result)
                {
                    _loggingService.LogInfo($"TryStartListening: Succès à la tentative {_startupAttempts}");
                    _lastSuccessfulOperation = DateTime.Now;
                    _consecutiveHealthCheckFailures = 0;
                    StartHealthMonitoring();
                }
                else if (_startupAttempts < _options.StartupRetryCount)
                {
                    ScheduleIntelligentRetry();
                }
                return result;
            }
            else
            {
                // Mode non-bloquant - plus complexe mais ne bloque pas le thread appelant
                try
                {
                    bool result = _dispatcherService.InvokeAsync(() => InitializeWindow()).GetAwaiter().GetResult();
                    if (result)
                    {
                        _loggingService.LogInfo($"TryStartListening: Succès à la tentative {_startupAttempts}");
                        _lastSuccessfulOperation = DateTime.Now;
                        _consecutiveHealthCheckFailures = 0;
                        StartHealthMonitoring();
                    }
                    else if (_startupAttempts < _options.StartupRetryCount)
                    {
                        ScheduleIntelligentRetry();
                    }
                    return result;
                }
                catch (Exception ex)
                {
                    _loggingService.LogError("StartListening: Erreur lors de l'initialisation asynchrone", ex);
                    if (_startupAttempts < _options.StartupRetryCount)
                    {
                        ScheduleIntelligentRetry();
                    }
                    return false;
                }
            }
        }
        
        private void ScheduleIntelligentRetry()
        {
            int delay = CalculateRetryDelay();
            _loggingService.LogInfo($"ScheduleIntelligentRetry: Planification d'une nouvelle tentative dans {delay}ms (tentative {_startupAttempts + 1}/{_options.StartupRetryCount})");

            _reconnectTimer?.Dispose();
            _reconnectTimer = new Timer(
                _ => TryStartListening(),
                null,
                delay,
                Timeout.Infinite);
        }

        private int CalculateRetryDelay()
        {
            if (!_options.UseExponentialBackoff)
            {
                return _options.StartupRetryDelayMs;
            }

            // Backoff exponentiel : délai = délai_base * 2^(tentative-1)
            int exponentialDelay = _options.StartupRetryDelayMs * (int)Math.Pow(2, _startupAttempts - 1);

            // Limiter le délai maximum
            int finalDelay = Math.Min(exponentialDelay, _options.MaxRetryDelayMs);

            _loggingService.LogInfo($"CalculateRetryDelay: Tentative {_startupAttempts}, délai calculé: {exponentialDelay}ms, délai final: {finalDelay}ms");

            return finalDelay;
        }

        private void StartHealthMonitoring()
        {
            if (!_options.EnableHealthMonitoring)
            {
                _loggingService.LogInfo("StartHealthMonitoring: Monitoring de santé désactivé");
                return;
            }

            _loggingService.LogInfo($"StartHealthMonitoring: Démarrage du monitoring de santé (intervalle: {_options.HealthCheckIntervalMs}ms)");

            _healthCheckTimer?.Dispose();
            _healthCheckTimer = new Timer(
                _ => PerformHealthCheck(),
                null,
                _options.HealthCheckIntervalMs,
                _options.HealthCheckIntervalMs);
        }

        private void PerformHealthCheck()
        {
            try
            {
                _loggingService.LogDebug("PerformHealthCheck: Vérification de santé en cours...");

                // Vérifier si le service est toujours en écoute
                if (!_isListening)
                {
                    _loggingService.LogWarning("PerformHealthCheck: Service non en écoute - tentative de récupération");
                    AttemptRecovery();
                    return;
                }

                // Vérifier si la fenêtre et le handle sont toujours valides
                if (_windowHandle == IntPtr.Zero || !IsWindow(_windowHandle))
                {
                    _loggingService.LogWarning("PerformHealthCheck: Handle de fenêtre invalide - tentative de récupération");
                    AttemptRecovery();
                    return;
                }

                // Vérifier si on n'a pas eu d'opération réussie depuis trop longtemps
                var timeSinceLastSuccess = DateTime.Now - _lastSuccessfulOperation;
                if (timeSinceLastSuccess.TotalMinutes > 5) // 5 minutes sans succès
                {
                    _loggingService.LogWarning($"PerformHealthCheck: Aucune opération réussie depuis {timeSinceLastSuccess.TotalMinutes:F1} minutes - tentative de récupération");
                    AttemptRecovery();
                    return;
                }

                // Réinitialiser le compteur d'échecs si tout va bien
                _consecutiveHealthCheckFailures = 0;
                _loggingService.LogDebug("PerformHealthCheck: Service en bonne santé");
            }
            catch (Exception ex)
            {
                _consecutiveHealthCheckFailures++;
                _loggingService.LogError($"PerformHealthCheck: Erreur lors de la vérification de santé (échec #{_consecutiveHealthCheckFailures})", ex);

                if (_consecutiveHealthCheckFailures >= _options.HealthCheckFailureThreshold)
                {
                    _loggingService.LogWarning($"PerformHealthCheck: Seuil d'échecs atteint ({_consecutiveHealthCheckFailures}) - tentative de récupération");
                    AttemptRecovery();
                }
            }
        }

        private void AttemptRecovery()
        {
            _loggingService.LogInfo("AttemptRecovery: Début de la récupération automatique");

            try
            {
                // Arrêter le monitoring temporairement
                _healthCheckTimer?.Dispose();
                _healthCheckTimer = null;

                // Nettoyer les ressources
                CleanupResources();

                // Attendre un peu pour la stabilisation
                Thread.Sleep(1000);

                // Réinitialiser les compteurs
                _startupAttempts = 0;
                _consecutiveHealthCheckFailures = 0;

                // Tenter de redémarrer
                _loggingService.LogInfo("AttemptRecovery: Tentative de redémarrage...");
                if (TryStartListening())
                {
                    _loggingService.LogInfo("AttemptRecovery: Récupération réussie !");
                }
                else
                {
                    _loggingService.LogError("AttemptRecovery: Échec de la récupération");
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError("AttemptRecovery: Erreur lors de la récupération automatique", ex);
            }
        }

        /// <summary>
        /// Arrête l'écoute du presse-papier
        /// </summary>
        public virtual void StopListening()
        {
            lock (_lock)
            {
                if (_isDisposed)
                {
                    _loggingService.LogError("StopListening: Tentative d'utilisation d'un service déjà libéré");
                    throw new ObjectDisposedException(nameof(ClipboardListenerService));
                }
                    
                if (!_isListening)
                {
                    _loggingService.LogInfo("StopListening: Le service n'est pas en écoute");
                    return;
                }
                
                _loggingService.LogInfo("StopListening: Arrêt de l'écoute");

                // Annuler toute tentative de reconnexion en cours
                _reconnectTimer?.Dispose();
                _reconnectTimer = null;

                // Arrêter le monitoring de santé
                _healthCheckTimer?.Dispose();
                _healthCheckTimer = null;
                
                // Utiliser le mode approprié selon la configuration
                Action cleanup = () =>
                {
                    try
                    {
                        CleanupResources();
                    }
                    catch (Exception ex)
                    {
                        _loggingService.LogError("StopListening: Erreur lors de l'arrêt", ex);
                    }
                };
                
                if (_options.UseBlockingUIOperations)
                {
                    _dispatcherService.Invoke(cleanup);
                }
                else
                {
                    _dispatcherService.InvokeAsync(cleanup);
                }
            }
        }
        
        #endregion
        
        #region Méthodes privées
        
        private bool InitializeWindow()
        {
            try
            {
                if (_hiddenWindow == null)
                {
                    _loggingService.LogInfo("InitializeWindow: Création de la fenêtre cachée");
                    _hiddenWindow = new Window
                    {
                        Width = 0,
                        Height = 0,
                        ShowInTaskbar = false,
                        WindowStyle = WindowStyle.None,
                        Title = "ClipboardListenerWindow"
                    };
                    
                    _hiddenWindow.SourceInitialized += OnHiddenWindowSourceInitialized;
                    _hiddenWindow.Closed += OnHiddenWindowClosed;
                    _hiddenWindow.Show();
                    _hiddenWindow.Hide(); // Cacher mais garder actif
                    _loggingService.LogInfo("InitializeWindow: Fenêtre cachée créée avec succès");
                }
                else
                {
                    _loggingService.LogInfo("InitializeWindow: Fenêtre cachée déjà existante, réutilisation");
                    // La fenêtre existe déjà, vérifier qu'elle est valide
                    var windowHelper = new WindowInteropHelper(_hiddenWindow);
                    if (windowHelper.Handle == IntPtr.Zero || !IsWindow(windowHelper.Handle))
                    {
                        _loggingService.LogWarning("InitializeWindow: Fenêtre invalide, recréation nécessaire");
                        CleanupResources();
                        return InitializeWindow(); // Réessayer avec une nouvelle fenêtre
                    }
                    
                    // Si la fenêtre est valide mais que nous ne sommes pas en écoute, réinitialiser l'écoute
                    if (!_isListening)
                    {
                        _loggingService.LogInfo("InitializeWindow: Fenêtre valide mais pas en écoute, réinitialisation");
                        OnHiddenWindowSourceInitialized(_hiddenWindow, EventArgs.Empty);
                    }
                }
                
                // Le reste de l'initialisation sera fait dans OnHiddenWindowSourceInitialized
                return true;
            }
            catch (Exception ex)
            {
                _loggingService.LogError("InitializeWindow: Erreur lors du démarrage", ex);
                return false;
            }
        }
        
        private void OnHiddenWindowClosed(object? sender, EventArgs e)
        {
            _loggingService.LogInfo("OnHiddenWindowClosed: La fenêtre cachée a été fermée");
            _isListening = false;
        }
        
        private void OnHiddenWindowSourceInitialized(object? sender, EventArgs e)
        {
            try
            {
                if (_hiddenWindow == null)
                {
                    _loggingService.LogError("OnHiddenWindowSourceInitialized: La fenêtre cachée est null");
                    return;
                }
                
                var windowHelper = new WindowInteropHelper(_hiddenWindow);
                _windowHandle = windowHelper.Handle;
                
                if (_windowHandle == IntPtr.Zero)
                {
                    _loggingService.LogError("OnHiddenWindowSourceInitialized: Handle de fenêtre invalide");
                    return;
                }
                
                _source = HwndSource.FromHwnd(_windowHandle);
                
                if (_source == null)
                {
                    _loggingService.LogError("OnHiddenWindowSourceInitialized: Impossible d'obtenir la source HWND");
                    return;
                }
                
                _source.AddHook(WndProc);
                
                bool result = AddClipboardFormatListener(_windowHandle);
                if (!result)
                {
                    int error = Marshal.GetLastWin32Error();
                    _loggingService.LogError($"OnHiddenWindowSourceInitialized: Échec de AddClipboardFormatListener, code d'erreur: {error}");
                    
                    if (error == ERROR_INVALID_WINDOW_HANDLE || error == ERROR_INVALID_PARAMETER)
                    {
                        _loggingService.LogWarning("OnHiddenWindowSourceInitialized: Handle de fenêtre invalide, tentative de recréation");
                        CleanupResources();
                        InitializeWindow();
                    }
                    
                    return;
                }
                
                _isListening = true;
                _loggingService.LogInfo("OnHiddenWindowSourceInitialized: Écoute du presse-papier démarrée avec succès");
            }
            catch (Exception ex)
            {
                _loggingService.LogError("OnHiddenWindowSourceInitialized: Erreur lors de l'initialisation", ex);
            }
        }
        
        private IntPtr WndProc(IntPtr hwnd, int msg, IntPtr wParam, IntPtr lParam, ref bool handled)
        {
            if (msg == WM_CLIPBOARDUPDATE)
            {
                string eventId = Guid.NewGuid().ToString();
                DateTime now = DateTime.Now;
                
                // Appliquer le debouncing pour éviter les événements multiples rapprochés
                if ((now - _lastEventTime) < _debounceInterval && _lastEventId != string.Empty)
                {
                    _loggingService.LogDebug($"WndProc: Événement ignoré par debounce, eventId: {eventId}, dernier: {_lastEventId}");
                    _lastEventTime = now;
                    _lastEventId = eventId;
                    handled = true;
                    return IntPtr.Zero;
                }
                
                _lastEventTime = now;
                _lastEventId = eventId;
                
                _loggingService.LogDebug($"WndProc: Contenu du presse-papier modifié, eventId: {eventId}");
                
                try
                {
                    OnClipboardContentChanged(EventArgs.Empty);
                    handled = true;
                }
                catch (Exception ex)
                {
                    _loggingService.LogError($"WndProc: Erreur lors du traitement de l'événement, eventId: {eventId}", ex);
                }
            }
            
            return IntPtr.Zero;
        }
        
        private void CleanupResources()
        {
            try
            {
                _loggingService.LogInfo("CleanupResources: Nettoyage des ressources");
                
                if (_source != null)
                {
                    if (_windowHandle != IntPtr.Zero)
                    {
                        try
                        {
                            if (IsWindow(_windowHandle))
                            {
                                RemoveClipboardFormatListener(_windowHandle);
                                _loggingService.LogInfo("CleanupResources: Listener du presse-papier retiré");
                            }
                        }
                        catch (Exception ex)
                        {
                            _loggingService.LogError("CleanupResources: Erreur lors du retrait du listener", ex);
                        }
                    }
                    
                    _source.RemoveHook(WndProc);
                    _source = null;
                    _loggingService.LogInfo("CleanupResources: Hook WndProc retiré");
                }
                
                if (_hiddenWindow != null)
                {
                    _hiddenWindow.SourceInitialized -= OnHiddenWindowSourceInitialized;
                    _hiddenWindow.Closed -= OnHiddenWindowClosed;
                    
                    try
                    {
                        _hiddenWindow.Close();
                        _loggingService.LogInfo("CleanupResources: Fenêtre cachée fermée");
                    }
                    catch (Exception ex)
                    {
                        _loggingService.LogError("CleanupResources: Erreur lors de la fermeture de la fenêtre cachée", ex);
                    }
                    
                    _hiddenWindow = null;
                }
                
                _windowHandle = IntPtr.Zero;
                _isListening = false;
                _loggingService.LogInfo("CleanupResources: Ressources nettoyées avec succès");
            }
            catch (Exception ex)
            {
                _loggingService.LogError("CleanupResources: Erreur lors du nettoyage des ressources", ex);
                // Réinitialiser les champs même en cas d'erreur
                _source = null;
                _hiddenWindow = null;
                _windowHandle = IntPtr.Zero;
                _isListening = false;
            }
        }
        
        protected virtual void OnClipboardContentChanged(EventArgs e)
        {
            try
            {
                _loggingService.LogDebug($"OnClipboardContentChanged: Déclenchement de l'événement, eventId: {_lastEventId}");
                
                // Copier le gestionnaire d'événements pour éviter les problèmes de thread-safety
                var handler = _clipboardContentChangedHandler;
                if (handler != null)
                {
                    // Exécuter sur le thread UI pour éviter les problèmes de thread-safety
                    _dispatcherService.Invoke(() => handler(this, e));
                    _loggingService.LogDebug($"OnClipboardContentChanged: Événement déclenché avec succès, eventId: {_lastEventId}");

                    // Marquer l'opération comme réussie pour le monitoring de santé
                    _lastSuccessfulOperation = DateTime.Now;
                }
                else
                {
                    _loggingService.LogWarning($"OnClipboardContentChanged: Aucun gestionnaire d'événement enregistré, eventId: {_lastEventId}");
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"OnClipboardContentChanged: Erreur lors du déclenchement de l'événement, eventId: {_lastEventId}", ex);
            }
        }
        
        #endregion
        
        #region IDisposable
        
        /// <summary>
        /// Libère les ressources utilisées par le service
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
        
        /// <summary>
        /// Libère les ressources utilisées par le service
        /// </summary>
        /// <param name="disposing">Indique si les ressources managées doivent être libérées</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_isDisposed)
            {
                if (disposing)
                {
                    _loggingService.LogInfo("Dispose: Libération des ressources");
                    _reconnectTimer?.Dispose();
                    _reconnectTimer = null;
                    _healthCheckTimer?.Dispose();
                    _healthCheckTimer = null;
                    StopListening();
                    _clipboardContentChangedHandler = null;
                    _loggingService.LogInfo("Dispose: Ressources libérées");
                }
                
                _isDisposed = true;
            }
        }
        
        #endregion
    }
} 