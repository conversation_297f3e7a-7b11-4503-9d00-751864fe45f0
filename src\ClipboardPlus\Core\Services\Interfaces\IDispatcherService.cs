
using System;
using System.Threading.Tasks;

namespace ClipboardPlus.Core.Services
{
    /// <summary>
    /// Interface d'abstraction pour les services de dispatcher.
    /// Respecte le Principe d'Inversion des Dépendances (DIP).
    /// </summary>
    public interface IDispatcherService
    {
        /// <summary>
        /// Exécute une action de manière synchrone sur le thread UI
        /// </summary>
        void Invoke(Action action);

        /// <summary>
        /// Exécute une fonction de manière synchrone sur le thread UI
        /// </summary>
        T Invoke<T>(Func<T> function);

        /// <summary>
        /// Exécute une action de manière asynchrone sur le thread UI
        /// </summary>
        Task InvokeAsync(Action action);

        /// <summary>
        /// Exécute une fonction de manière asynchrone sur le thread UI
        /// </summary>
        Task<T> InvokeAsync<T>(Func<T> function);
        
        /// <summary>
        /// Détermine si le thread appelant a accès à ce Dispatcher.
        /// </summary>
        bool CheckAccess();

        /// <summary>
        /// Exécute une fonction de tâche de manière asynchrone sur le thread UI
        /// </summary>
        Task InvokeAsync(Func<Task> taskFunc);

        /// <summary>
        /// Exécute une fonction de tâche de manière asynchrone sur le thread UI
        /// </summary>
        Task<T> InvokeAsync<T>(Func<Task<T>> taskFunc);
    }
}
