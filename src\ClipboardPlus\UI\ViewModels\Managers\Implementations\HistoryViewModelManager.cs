// ============================================================================
// HISTORY VIEWMODEL MANAGER IMPLEMENTATION - PHASE 6C
// ============================================================================
//
// 🎯 OBJECTIF : Implémentation concrète de la gestion de l'historique
// 📋 RESPONSABILITÉ : Délégation pure vers HistoryModule existant
// 🏗️ ARCHITECTURE : Réutilisation complète de l'infrastructure existante
//
// ============================================================================

using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Modules.History;
using ClipboardPlus.UI.ViewModels.Managers.Interfaces;
using CommunityToolkit.Mvvm.ComponentModel;

namespace ClipboardPlus.UI.ViewModels.Managers.Implementations
{
    /// <summary>
    /// Implémentation concrète du manager de gestion de l'historique.
    /// 
    /// Cette classe délègue toutes les opérations vers le HistoryModule existant
    /// et maintient la synchronisation avec les collections observables pour l'UI.
    /// </summary>
    public class HistoryViewModelManager : ObservableObject, IHistoryViewModelManager
    {
        #region Champs Privés

        private readonly IHistoryModule _historyModule;
        private ClipboardItem? _selectedClipboardItem;
        private string? _searchText;
        private bool _isLoading;
        private bool _isDisposed;

        #endregion

        #region Constructeur

        /// <summary>
        /// Initialise une nouvelle instance du HistoryViewModelManager.
        /// </summary>
        /// <param name="historyModule">Module d'historique à utiliser</param>
        public HistoryViewModelManager(IHistoryModule historyModule)
        {
            _historyModule = historyModule ?? throw new ArgumentNullException(nameof(historyModule));
            
            // Initialiser les collections observables
            HistoryItems = new ObservableCollection<ClipboardItem>();
            
            // S'abonner aux événements du module
            SubscribeToModuleEvents();
        }

        #endregion

        #region Propriétés Observables (IHistoryViewModelManager)

        /// <summary>
        /// Collection observable des éléments d'historique affichés dans l'UI.
        /// </summary>
        public ObservableCollection<ClipboardItem> HistoryItems { get; }

        /// <summary>
        /// Élément actuellement sélectionné dans l'interface utilisateur.
        /// </summary>
        public ClipboardItem? SelectedClipboardItem
        {
            get => _selectedClipboardItem;
            set
            {
                if (SetProperty(ref _selectedClipboardItem, value))
                {
                    SelectionChanged?.Invoke(this, value);
                }
            }
        }

        /// <summary>
        /// Texte de recherche/filtrage actuel.
        /// </summary>
        public string? SearchText
        {
            get => _searchText;
            set
            {
                if (SetProperty(ref _searchText, value))
                {
                    SearchFilterChanged?.Invoke(this, value);
                    // Appliquer le filtre automatiquement
                    ApplySearchFilter(value);
                }
            }
        }

        /// <summary>
        /// Indique si une opération de chargement est en cours.
        /// </summary>
        public bool IsLoading
        {
            get => _isLoading;
            private set
            {
                if (SetProperty(ref _isLoading, value))
                {
                    LoadingStateChanged?.Invoke(this, value);
                }
            }
        }

        /// <summary>
        /// Nombre total d'éléments dans l'historique (avant filtrage).
        /// </summary>
        public int TotalItemCount => _historyModule.TotalItemCount;

        /// <summary>
        /// Nombre d'éléments visibles après filtrage.
        /// </summary>
        public int FilteredItemCount => _historyModule.FilteredItemCount;

        #endregion

        #region Événements (IHistoryViewModelManager)

        /// <summary>
        /// Événement déclenché lorsque l'historique change.
        /// </summary>
        public event EventHandler? HistoryChanged;

        /// <summary>
        /// Événement déclenché lorsque la sélection change.
        /// </summary>
        public event EventHandler<ClipboardItem?>? SelectionChanged;

        /// <summary>
        /// Événement déclenché lorsque le filtre de recherche change.
        /// </summary>
        public event EventHandler<string?>? SearchFilterChanged;

        /// <summary>
        /// Événement déclenché lorsque l'état de chargement change.
        /// </summary>
        public event EventHandler<bool>? LoadingStateChanged;

        #endregion

        #region Méthodes de Gestion de l'Historique (IHistoryViewModelManager)

        /// <summary>
        /// Charge l'historique depuis la source de données via le HistoryModule.
        /// </summary>
        /// <param name="callContext">Contexte de l'appel pour le debugging</param>
        /// <returns>Task représentant l'opération de chargement</returns>
        public async Task LoadHistoryAsync(string callContext = "HistoryViewModelManager")
        {
            if (_isDisposed) return;

            try
            {
                IsLoading = true;
                
                // Déléguer vers le HistoryModule
                await _historyModule.LoadHistoryAsync(callContext);
                
                // Synchroniser les collections
                SynchronizeCollections();
                
                // Notifier le changement
                HistoryChanged?.Invoke(this, EventArgs.Empty);
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// Recharge l'historique en forçant une synchronisation.
        /// </summary>
        /// <param name="reason">Raison du rechargement</param>
        /// <returns>Task représentant l'opération de rechargement</returns>
        public async Task ReloadHistoryAsync(string reason = "Manual reload")
        {
            if (_isDisposed) return;

            try
            {
                IsLoading = true;

                // Forcer le rechargement via le module
                await _historyModule.ReloadHistoryAsync(reason);

                // Synchroniser les collections
                SynchronizeCollections();

                // Notifier le changement
                HistoryChanged?.Invoke(this, EventArgs.Empty);
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// Force une synchronisation des collections avec le HistoryModule.
        /// </summary>
        /// <param name="reason">Raison de la synchronisation forcée</param>
        /// <returns>Task représentant l'opération de synchronisation</returns>
        public async Task ForceSynchronizationAsync(string reason = "Manual sync")
        {
            if (_isDisposed) return;

            try
            {
                IsLoading = true;

                // Forcer la synchronisation via le module
                await _historyModule.ForceSynchronizationAsync(reason);

                // Synchroniser les collections
                SynchronizeCollections();

                // Notifier le changement
                HistoryChanged?.Invoke(this, EventArgs.Empty);
            }
            finally
            {
                IsLoading = false;
            }
        }

        #endregion

        #region Méthodes de Filtrage et Recherche (IHistoryViewModelManager)

        /// <summary>
        /// Applique un filtre de recherche sur les éléments d'historique.
        /// </summary>
        /// <param name="searchFilter">Filtre à appliquer (null pour effacer)</param>
        public void ApplySearchFilter(string? searchFilter)
        {
            if (_isDisposed) return;

            // Déléguer vers le HistoryModule
            _historyModule.ApplyFilter(searchFilter);
            
            // Synchroniser les collections
            SynchronizeCollections();
            
            // Notifier le changement
            HistoryChanged?.Invoke(this, EventArgs.Empty);
        }

        /// <summary>
        /// Efface le filtre de recherche actuel.
        /// </summary>
        public void ClearSearchFilter()
        {
            SearchText = null;
        }

        #endregion

        #region Méthodes Privées

        /// <summary>
        /// S'abonne aux événements du HistoryModule.
        /// </summary>
        private void SubscribeToModuleEvents()
        {
            if (_historyModule != null)
            {
                // S'abonner aux changements du module
                // Note: Les événements spécifiques dépendent de l'implémentation du HistoryModule
                // Cette partie sera adaptée selon l'interface réelle du module
            }
        }

        /// <summary>
        /// Synchronise les collections observables avec le HistoryModule.
        /// </summary>
        private void SynchronizeCollections()
        {
            if (_isDisposed) return;

            // Synchroniser HistoryItems avec FilteredItems du module
            var moduleItems = _historyModule.FilteredItems;
            
            // Mise à jour efficace de la collection observable
            HistoryItems.Clear();
            foreach (var item in moduleItems)
            {
                HistoryItems.Add(item);
            }
        }

        /// <summary>
        /// Se désabonne des événements du HistoryModule.
        /// </summary>
        private void UnsubscribeFromModuleEvents()
        {
            // Se désabonner des événements du module
            // Cette partie sera adaptée selon l'interface réelle du module
        }

        #endregion

        #region Méthodes d'Initialisation et Nettoyage (IHistoryViewModelManager)

        /// <summary>
        /// Initialise le manager avec les dépendances nécessaires.
        /// </summary>
        /// <returns>Task représentant l'initialisation</returns>
        public async Task InitializeAsync()
        {
            if (_isDisposed) return;

            // Charger l'historique initial
            await LoadHistoryAsync("Initialization");
        }

        /// <summary>
        /// Nettoie les ressources et se désabonne des événements.
        /// </summary>
        public void Cleanup()
        {
            if (_isDisposed) return;

            UnsubscribeFromModuleEvents();
            HistoryItems.Clear();
            _selectedClipboardItem = null;
            _searchText = null;
        }

        /// <summary>
        /// Libère les ressources utilisées par le manager.
        /// </summary>
        public void Dispose()
        {
            if (_isDisposed) return;

            Cleanup();
            _isDisposed = true;
        }

        #endregion

        #region Méthodes Non Implémentées (À compléter selon les besoins)

        // Les méthodes suivantes de l'interface seront implémentées dans une prochaine itération
        // selon les besoins spécifiques identifiés lors de l'intégration

        public void SelectItem(ClipboardItem? item) => SelectedClipboardItem = item;
        public void SelectNextItem() { /* À implémenter */ }
        public void SelectPreviousItem() { /* À implémenter */ }
        public void SelectFirstItem() { /* À implémenter */ }
        public void SelectLastItem() { /* À implémenter */ }
        public ClipboardItem? FindItemById(long id) => HistoryItems.FirstOrDefault(i => i.Id == id);
        public bool IsItemVisible(ClipboardItem item) => HistoryItems.Contains(item);
        public int GetItemIndex(ClipboardItem item) => HistoryItems.IndexOf(item);
        public HistoryStatistics GetStatistics() => new HistoryStatistics
        {
            TotalItems = TotalItemCount,
            FilteredItems = FilteredItemCount,
            CurrentFilter = SearchText
        };

        #endregion
    }
}
