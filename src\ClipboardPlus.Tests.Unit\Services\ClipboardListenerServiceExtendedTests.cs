using System;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Services;
using ClipboardPlus.Services.Interfaces;
using Moq;
using NUnit.Framework;

namespace ClipboardPlus.Tests.Unit.Services
{
    [TestFixture]
    public class ClipboardListenerServiceExtendedTests
    {
        private Mock<ILoggingService> _mockLoggingService = null!;
        private Mock<IDispatcherService> _mockDispatcherService = null!;
        private ClipboardListenerOptions _options = null!;
        private ClipboardListenerService _service = null!;

        [SetUp]
        public void Setup()
        {
            _mockLoggingService = new Mock<ILoggingService>();
            _mockDispatcherService = new Mock<IDispatcherService>();
            
            // Configurer le mock du dispatcher pour simuler l'exécution sur le thread UI
            _mockDispatcherService.Setup(d => d.CheckAccess()).Returns(true);
            _mockDispatcherService.Setup(d => d.Invoke(It.IsAny<Action>()))
                .Callback<Action>(action => action());
            _mockDispatcherService.Setup(d => d.Invoke<bool>(It.IsAny<Func<bool>>()))
                .Returns<Func<bool>>(func => func());
            
            _options = new ClipboardListenerOptions
            {
                DebounceIntervalMs = 50,
                StartupRetryCount = 2,
                StartupRetryDelayMs = 10,
                UseBlockingUIOperations = true
            };
            
            _service = new ClipboardListenerService(_mockLoggingService.Object, _mockDispatcherService.Object, _options);
        }

        [TearDown]
        public void TearDown()
        {
            _service.Dispose();
        }

        [Test]
        public void Constructor_WithNullLoggingService_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new ClipboardListenerService(null!, _mockDispatcherService.Object, _options));
        }

        [Test]
        public void Constructor_WithNullDispatcherService_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new ClipboardListenerService(_mockLoggingService.Object, null!, _options));
        }

        [Test]
        public void Constructor_WithNullOptions_ShouldUseDefaultOptions()
        {
            // Act
            var service = new ClipboardListenerService(_mockLoggingService.Object, _mockDispatcherService.Object, null);
            
            // Assert
            Assert.That(service, Is.Not.Null);
            Assert.That(_mockLoggingService.Invocations.Count, Is.GreaterThan(0));
        }

        [Test]
        public void Constructor_ShouldInitializeRequiredFields()
        {
            // Arrange
            Type type = typeof(ClipboardListenerService);
            
            // Act
            var service = new ClipboardListenerService(_mockLoggingService.Object, _mockDispatcherService.Object, _options);
            
            // Assert
            Assert.That(GetPrivateField(service, "_isListening"), Is.False);
            Assert.That(GetPrivateField(service, "_isDisposed"), Is.False);
            Assert.That(GetPrivateField(service, "_startupAttempts"), Is.EqualTo(0));
            Assert.That(GetPrivateField(service, "_loggingService"), Is.Not.Null);
            Assert.That(GetPrivateField(service, "_dispatcherService"), Is.Not.Null);
        }

        [Test]
        public void StartListening_WhenAlreadyListening_ShouldReturnTrue()
        {
            // Arrange
            // Simuler que le service est déjà en écoute
            SetPrivateField(_service, "_isListening", true);
            
            // Act
            bool result = _service.StartListening();
            
            // Assert
            Assert.That(result, Is.True);
            Assert.That(_mockLoggingService.Invocations.Count, Is.GreaterThan(0));
        }

        [Test]
        public void StartListening_WhenDisposed_ShouldThrowObjectDisposedException()
        {
            // Arrange
            // Simuler que le service est disposé
            SetPrivateField(_service, "_isDisposed", true);
            
            // Act & Assert
            Assert.Throws<ObjectDisposedException>(() => _service.StartListening());
            Assert.That(_mockLoggingService.Invocations.Count, Is.GreaterThan(0));
        }

        [Test]
        public void StopListening_WhenNotListening_ShouldDoNothing()
        {
            // Arrange
            // Simuler que le service n'est pas en écoute
            SetPrivateField(_service, "_isListening", false);
            
            // Act
            _service.StopListening();
            
            // Assert
            Assert.That(_mockLoggingService.Invocations.Count, Is.GreaterThan(0));
        }

        [Test]
        public void StopListening_WhenDisposed_ShouldThrowObjectDisposedException()
        {
            // Arrange
            // Simuler que le service est disposé
            SetPrivateField(_service, "_isDisposed", true);
            
            // Act & Assert
            Assert.Throws<ObjectDisposedException>(() => _service.StopListening());
            Assert.That(_mockLoggingService.Invocations.Count, Is.GreaterThan(0));
        }

        [Test]
        public void Dispose_ShouldCleanupResources()
        {
            // Arrange
            // Simuler que le service est en écoute
            SetPrivateField(_service, "_isListening", true);
            
            // Act
            _service.Dispose();
            
            // Assert
            var isDisposed = (bool)GetPrivateField(_service, "_isDisposed");
            Assert.That(isDisposed, Is.True);
        }

        [Test]
        public void Dispose_WhenCalledMultipleTimes_ShouldOnlyDisposeOnce()
        {
            // Arrange
            // Simuler que le service est en écoute
            SetPrivateField(_service, "_isListening", true);
            
            // Act
            _service.Dispose();
            int invocationCount = _mockLoggingService.Invocations.Count;
            _mockLoggingService.Invocations.Clear(); // Réinitialiser les appels
            _service.Dispose(); // Deuxième appel
            
            // Assert
            Assert.That(_mockLoggingService.Invocations.Count, Is.EqualTo(0));
        }

        [Test]
        public void ClipboardContentChanged_ShouldAddAndRemoveHandlerCorrectly()
        {
            // Arrange
            EventHandler handler = (s, e) => { };
            
            // Act
            _service.ClipboardContentChanged += handler;
            
            // Assert
            Assert.That(_mockLoggingService.Invocations.Count, Is.GreaterThan(0));
            
            // Réinitialiser les appels
            _mockLoggingService.Invocations.Clear();
            
            // Act
            _service.ClipboardContentChanged -= handler;
            
            // Assert
            Assert.That(_mockLoggingService.Invocations.Count, Is.GreaterThan(0));
        }

        [Test]
        public void TryStartListening_WhenMaxAttemptsReached_ShouldReturnFalse()
        {
            // Arrange
            // Configurer le mock du dispatcher pour simuler un échec d'initialisation
            _mockDispatcherService.Setup(d => d.Invoke<bool>(It.IsAny<Func<bool>>()))
                .Returns(false);
            
            // Simuler que le nombre maximum de tentatives est atteint
            SetPrivateField(_service, "_startupAttempts", _options.StartupRetryCount);
            
            // Act
            var method = typeof(ClipboardListenerService).GetMethod("TryStartListening", 
                BindingFlags.NonPublic | BindingFlags.Instance);
            var result = (bool)method!.Invoke(_service, null)!;
            
            // Assert
            Assert.That(result, Is.False);
            Assert.That(_mockLoggingService.Invocations.Count, Is.GreaterThan(0));
        }

        [Test]
        public void IsListening_WhenListening_ShouldReturnTrue()
        {
            // Arrange
            SetPrivateField(_service, "_isListening", true);
            
            // Act
            bool result = _service.IsListening;
            
            // Assert
            Assert.That(result, Is.True);
        }

        [Test]
        public void IsListening_WhenNotListening_ShouldReturnFalse()
        {
            // Arrange
            SetPrivateField(_service, "_isListening", false);
            
            // Act
            bool result = _service.IsListening;
            
            // Assert
            Assert.That(result, Is.False);
        }

        [Test]
        public void OnClipboardContentChanged_ShouldInvokeEventHandlers()
        {
            // Arrange
            bool eventRaised = false;
            EventHandler handler = (s, e) => { eventRaised = true; };
            _service.ClipboardContentChanged += handler;
            
            // Act
            var method = typeof(ClipboardListenerService).GetMethod("OnClipboardContentChanged", 
                BindingFlags.NonPublic | BindingFlags.Instance);
            method!.Invoke(_service, new object[] { EventArgs.Empty });
            
            // Assert
            Assert.That(eventRaised, Is.True);
            
            // Cleanup
            _service.ClipboardContentChanged -= handler;
        }

        [Test]
        public void OnClipboardContentChanged_WithoutSubscribers_ShouldNotThrow()
        {
            // Arrange
            // S'assurer qu'il n'y a pas d'abonnés à l'événement
            var eventField = typeof(ClipboardListenerService).GetField("ClipboardContentChanged", 
                BindingFlags.NonPublic | BindingFlags.Instance);
            
            // Act & Assert
            var method = typeof(ClipboardListenerService).GetMethod("OnClipboardContentChanged", 
                BindingFlags.NonPublic | BindingFlags.Instance);
            Assert.DoesNotThrow(() => method!.Invoke(_service, new object[] { EventArgs.Empty }));
        }

        [Test]
        public void ClipboardListenerService_ShouldImplementIDisposable()
        {
            // Arrange & Act
            Type type = typeof(ClipboardListenerService);
            
            // Assert
            Assert.That(typeof(IDisposable).IsAssignableFrom(type), Is.True,
                "ClipboardListenerService devrait implémenter IDisposable");
        }

        [Test]
        public void ClipboardListenerService_ShouldImplementIClipboardListenerService()
        {
            // Arrange & Act
            Type type = typeof(ClipboardListenerService);
            
            // Assert
            Assert.That(typeof(IClipboardListenerService).IsAssignableFrom(type), Is.True,
                "ClipboardListenerService devrait implémenter IClipboardListenerService");
        }

        [Test]
        public void ClipboardListenerService_RequiredMethods_ShouldExist()
        {
            // Arrange & Act
            Type type = typeof(ClipboardListenerService);
            
            // Assert
            Assert.That(type.GetMethod("StartListening"), Is.Not.Null, "La méthode StartListening devrait exister");
            Assert.That(type.GetMethod("StopListening"), Is.Not.Null, "La méthode StopListening devrait exister");
            Assert.That(type.GetMethod("Dispose"), Is.Not.Null, "La méthode Dispose devrait exister");
            Assert.That(type.GetMethod("TryStartListening", BindingFlags.NonPublic | BindingFlags.Instance), 
                Is.Not.Null, "La méthode privée TryStartListening devrait exister");
            Assert.That(type.GetMethod("OnClipboardContentChanged", BindingFlags.NonPublic | BindingFlags.Instance), 
                Is.Not.Null, "La méthode privée OnClipboardContentChanged devrait exister");
        }

        // Méthodes utilitaires pour accéder aux champs privés
        private void SetPrivateField(object obj, string fieldName, object value)
        {
            var field = obj.GetType().GetField(fieldName, BindingFlags.NonPublic | BindingFlags.Instance);
            field?.SetValue(obj, value);
        }
        
        private object GetPrivateField(object obj, string fieldName)
        {
            var field = obj.GetType().GetField(fieldName, BindingFlags.NonPublic | BindingFlags.Instance);
            return field?.GetValue(obj) ?? throw new InvalidOperationException($"Field {fieldName} not found");
        }
    }
} 