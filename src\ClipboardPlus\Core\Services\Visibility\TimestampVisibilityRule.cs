using System;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Core.Services.Visibility
{
    /// <summary>
    /// Règle de visibilité spécifique pour les horodatages des éléments
    /// Respecte le Single Responsibility Principle : une seule responsabilité - évaluer la visibilité des horodatages
    /// Implémente le Strategy Pattern pour permettre l'extensibilité
    /// </summary>
    public class TimestampVisibilityRule : IVisibilityRule<ClipboardItem>
    {
        private readonly ILoggingService? _loggingService;

        public TimestampVisibilityRule(ILoggingService? loggingService)
        {
            _loggingService = loggingService;
        }

        /// <summary>
        /// Détermine si l'horodatage d'un élément doit être visible
        /// Logique métier pure : visible si paramètre global activé
        /// </summary>
        /// <param name="item">Élément du presse-papiers à évaluer</param>
        /// <param name="context">Contexte global de visibilité</param>
        /// <returns>True si l'horodatage doit être visible, False sinon</returns>
        public bool ShouldBeVisible(ClipboardItem item, VisibilityContext context)
        {
            try
            {
                // Validation des paramètres
                if (item == null || context == null)
                {
                    _loggingService?.LogInfo($"[TIMESTAMP_VISIBILITY_RULE] Paramètres invalides - Item: {item != null}, Context: {context != null}");
                    return false;
                }

                // Règle métier simple :
                // La visibilité globale des horodatages détermine la visibilité
                // Tous les éléments ont un horodatage valide par défaut
                bool result = context.GlobalTimestampVisibility;

                _loggingService?.LogInfo($"[TIMESTAMP_VISIBILITY_RULE] Item ID: {item.Id}, GlobalTimestampVisibility: {context.GlobalTimestampVisibility}, Result: {result}");

                return result;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError("[TIMESTAMP_VISIBILITY_RULE] Exception in ShouldBeVisible. Returning default value 'true'.", ex);
                return true; // Default to visible in case of error
            }
        }
    }
}
