using ClipboardPlus.Core.Services;
using ClipboardPlus.Services;
using Moq;
using NUnit.Framework;
using System;
using System.Threading.Tasks;
using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.Tests.Unit.Services
{
    [TestFixture]
    public class ClipboardListenerIntegrationTests
    {
        private Mock<ILoggingService> _mockLogger = null!;
        private Mock<IDispatcherService> _mockDispatcher = null!;
        private Mock<IClipboardInteractionService> _mockClipboardInteraction = null!;
        private Mock<IClipboardHistoryManager> _mockHistoryManager = null!;
        private Mock<ISettingsManager> _mockSettingsManager = null!;
        private ClipboardListenerOptions _options = null!;
        private TestClipboardListenerService _listenerService = null!;
        private IClipboardProcessorService _processorService = null!;

        [SetUp]
        public void Setup()
        {
            _mockLogger = new Mock<ILoggingService>();
            _mockDispatcher = new Mock<IDispatcherService>();
            _mockClipboardInteraction = new Mock<IClipboardInteractionService>();
            _mockHistoryManager = new Mock<IClipboardHistoryManager>();
            _mockSettingsManager = new Mock<ISettingsManager>();
            
            // Configuration des mocks
            _mockDispatcher.Setup(d => d.CheckAccess()).Returns(true);
            _mockDispatcher.Setup(d => d.Invoke(It.IsAny<Action>()))
                .Callback<Action>(action => action());
            
            // Configurer toutes les propriétés nécessaires du SettingsManager
            _mockSettingsManager.SetupProperty(s => s.MaxHistoryItems, 50);
            _mockSettingsManager.SetupProperty(s => s.StartWithWindows, false);
            _mockSettingsManager.SetupProperty(s => s.MaxStorableItemSizeBytes, 1024 * 1024);
            _mockSettingsManager.SetupProperty(s => s.MaxTextPreviewLength, 100);
            _mockSettingsManager.SetupProperty(s => s.HideItemTitle, false);
            _mockSettingsManager.SetupProperty(s => s.HideTimestamp, false);
            
            _options = new ClipboardListenerOptions
            {
                DebounceIntervalMs = 50,
                StartupRetryCount = 1,
                StartupRetryDelayMs = 10
            };
            
            // Créer les services
            _listenerService = new TestClipboardListenerService(_mockLogger.Object, _mockDispatcher.Object, _options);
            _processorService = new ClipboardProcessorService(
                _mockLogger.Object,
                _mockHistoryManager.Object,
                _mockClipboardInteraction.Object,
                _mockSettingsManager.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _listenerService.Dispose();
        }

        [Test]
        public async Task WhenClipboardChanges_ProcessorShouldCaptureContent()
        {
            // Arrange
            bool processorCalled = false;
            
            // Simuler du texte dans le presse-papier
            _mockClipboardInteraction.Setup(c => c.ContainsText()).Returns(true);
            _mockClipboardInteraction.Setup(c => c.GetTextAsync()).ReturnsAsync("Test content");
            
            // Connecter le processeur au service d'écoute
            _listenerService.ClipboardContentChanged += async (s, e) => {
                await _processorService.ProcessCurrentClipboardContentAsync();
                processorCalled = true;
            };
            
            // Démarrer l'écoute
            _listenerService.StartListening();
            
            // Act
            _listenerService.SimulateClipboardChange();
            
            // Attendre que le traitement asynchrone soit terminé
            await Task.Delay(100);
            
            // Assert
            Assert.IsTrue(processorCalled, "Le processeur devrait être appelé quand le presse-papier change");
            _mockClipboardInteraction.Verify(c => c.ContainsText(), Times.Once);
            _mockClipboardInteraction.Verify(c => c.GetTextAsync(), Times.Once);
            _mockHistoryManager.Verify(h => h.AddItemAsync(It.IsAny<ClipboardItem>()), Times.Once);
        }

        [Test]
        public async Task WhenClipboardContainsNoText_ProcessorShouldNotAddToHistory()
        {
            // Arrange
            bool processorCalled = false;
            
            // Simuler qu'il n'y a pas de texte dans le presse-papier
            _mockClipboardInteraction.Setup(c => c.ContainsText()).Returns(false);
            _mockClipboardInteraction.Setup(c => c.ContainsImage()).Returns(false);
            _mockClipboardInteraction.Setup(c => c.ContainsFileDropList()).Returns(false);
            
            // Connecter le processeur au service d'écoute
            _listenerService.ClipboardContentChanged += async (s, e) => {
                await _processorService.ProcessCurrentClipboardContentAsync();
                processorCalled = true;
            };
            
            // Démarrer l'écoute
            _listenerService.StartListening();
            
            // Act
            _listenerService.SimulateClipboardChange();
            
            // Attendre que le traitement asynchrone soit terminé
            await Task.Delay(100);
            
            // Assert
            Assert.IsTrue(processorCalled, "Le processeur devrait être appelé quand le presse-papier change");
            _mockClipboardInteraction.Verify(c => c.ContainsText(), Times.Once);
            _mockHistoryManager.Verify(h => h.AddItemAsync(It.IsAny<ClipboardItem>()), Times.Never,
                "Aucun élément ne devrait être ajouté à l'historique quand le presse-papier ne contient pas de contenu compatible");
        }

        [Test]
        public async Task WhenListeningStops_ClipboardChangesShouldNotBeProcessed()
        {
            // Arrange
            int processCount = 0;
            
            // Simuler du texte dans le presse-papier
            _mockClipboardInteraction.Setup(c => c.ContainsText()).Returns(true);
            _mockClipboardInteraction.Setup(c => c.GetTextAsync()).ReturnsAsync("Test content");
            
            // Connecter le processeur au service d'écoute
            _listenerService.ClipboardContentChanged += async (s, e) => {
                await _processorService.ProcessCurrentClipboardContentAsync();
                processCount++;
            };
            
            // Démarrer l'écoute
            _listenerService.StartListening();
            
            // Act - Premier changement avec écoute active
            _listenerService.SimulateClipboardChange();
            
            // Attendre que le traitement asynchrone soit terminé
            await Task.Delay(100);
            
            // Arrêter l'écoute
            _listenerService.StopListening();
            
            // Assert
            Assert.AreEqual(1, processCount, "Le processeur ne devrait être appelé qu'une seule fois");
            _mockHistoryManager.Verify(h => h.AddItemAsync(It.IsAny<ClipboardItem>()), Times.Once);
        }

        [Test]
        public async Task MultipleRapidClipboardChanges_ShouldBeDebounced()
        {
            // Arrange
            int processCount = 0;
            
            // Simuler du texte dans le presse-papier
            _mockClipboardInteraction.Setup(c => c.ContainsText()).Returns(true);
            _mockClipboardInteraction.Setup(c => c.GetTextAsync()).ReturnsAsync("Test content");
            
            // Connecter le processeur au service d'écoute
            _listenerService.ClipboardContentChanged += async (s, e) => {
                await _processorService.ProcessCurrentClipboardContentAsync();
                processCount++;
            };
            
            // Démarrer l'écoute
            _listenerService.StartListening();
            
            // Act - Plusieurs changements rapides
            _listenerService.SimulateClipboardChange();
            _listenerService.SimulateClipboardChange(); // Devrait être ignoré par debounce
            _listenerService.SimulateClipboardChange(); // Devrait être ignoré par debounce
            
            // Assert
            Assert.AreEqual(1, processCount, "Les changements rapides devraient être debounced");
            
            // Act - Attendre que le délai de debounce soit écoulé puis simuler un autre changement
            await Task.Delay(100);
            _listenerService.SimulateClipboardChange();
            
            // Assert
            Assert.AreEqual(2, processCount, "Après le délai de debounce, un nouveau changement devrait être traité");
        }
    }
} 